import '../../domain/entities/password_reset_entity.dart';

/// Model for password reset request
class PasswordResetRequestModel extends PasswordResetRequestEntity {
  const PasswordResetRequestModel({
    required super.email,
  });

  /// Create model from JSON
  factory PasswordResetRequestModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetRequestModel(
      email: json['email'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  /// Convert model to entity
  PasswordResetRequestEntity toEntity() {
    return PasswordResetRequestEntity(
      email: email,
    );
  }
}

/// Model for password reset confirmation
class PasswordResetConfirmModel extends PasswordResetConfirmEntity {
  const PasswordResetConfirmModel({
    required super.email,
    required super.pin,
    required super.newPassword,
    required super.confirmPassword,
  });

  /// Create model from JSON
  factory PasswordResetConfirmModel.fromJson(Map<String, dynamic> json) {
    return PasswordResetConfirmModel(
      email: json['email'] as String,
      pin: json['pin'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'pin': pin,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }

  /// Convert model to entity
  PasswordResetConfirmEntity toEntity() {
    return PasswordResetConfirmEntity(
      email: email,
      pin: pin,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );
  }
}

/// Model for password reset response
class PasswordResetResponseModel extends PasswordResetResponseEntity {
  const PasswordResetResponseModel({
    required super.status,
    super.user,
    super.email,
    super.tokenValue,
    super.message,
  });

  /// Create model from JSON
  factory PasswordResetResponseModel.fromJson(Map<String, dynamic> json) {
    final bool status = json['status'] as bool? ?? false;
    if (status) {
      return PasswordResetResponseModel(
        status: status,
        user: json['user'] as String?,
        email: json['email'] as String?,
        tokenValue: json['tokenValue'] as String?,
      );
    } else {
      return PasswordResetResponseModel(
        status: status,
        message: json['message'] as String?,
      );
    }
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {
      'status': status,
    };
    if (status) {
      if (user != null) json['user'] = user;
      if (email != null) json['email'] = email;
      if (tokenValue != null) json['tokenValue'] = tokenValue;
    } else {
      if (message != null) json['message'] = message;
    }
    return json;
  }

  /// Convert model to entity
  PasswordResetResponseEntity toEntity() {
    return PasswordResetResponseEntity(
      status: status,
      user: user,
      email: email,
      tokenValue: tokenValue,
      message: message,
    );
  }
}
