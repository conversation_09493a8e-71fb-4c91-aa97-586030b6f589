import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:kairos/core/api/api_exception.dart';
import 'package:kairos/core/error/exceptions.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../models/finances_response_model.dart';
import '../models/finances_unpaid_response_model.dart';

/// Abstract interface for finances remote data source
abstract class FinancesRemoteDataSource {
  /// Get paid fees for a student
  Future<FinancesResponseModel> getPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered paid fees for a student with date range
  Future<FinancesResponseModel> getFilteredPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String dateDebut,
    required String dateFin,
    String? codeUtilisateur,
  });

  /// Get unpaid fees for a student
  Future<FinancesUnpaidResponseModel> getUnpaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}

/// Implementation of FinancesRemoteDataSource
class FinancesRemoteDataSourceImpl implements FinancesRemoteDataSource {
  final ApiClient apiClient;

  FinancesRemoteDataSourceImpl({required this.apiClient});



// ========================================== getPaidFees ====================================================
  @override
  Future<FinancesResponseModel> getPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      debugPrint('FinancesRemoteDataSourceImpl: Calling getPaidFees with params: $codeEtab, $telephone, $codeEtudiant, $codeUtilisateur'); // Log 1
      // Make HTTP GET request to the fraisPayes endpoint with query parameters
      final queryParams = {
          'codeEtab': codeEtab,
          'telephone': telephone.replaceAll("+", ""),
          'codeEtudiant': codeEtudiant
        };

        if(codeUtilisateur != null){
          queryParams['codeUtilisateur'] = codeUtilisateur;
        }
      final response = await apiClient.getWithToken(
        ApiEndpoints.getFraisPayes,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes),
      );

      debugPrint('FinancesRemoteDataSourceImpl: Received response with status code: ${response.statusCode}'); // Log 2
      debugPrint('FinancesRemoteDataSourceImpl: Raw response data: ${response.data}'); // Log 3

      final String responseJsonString = latin1.decode(response.data);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded response string: $responseJsonString'); // Log 4

      final decodedResponse = jsonDecode(responseJsonString);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded JSON object: $decodedResponse'); // Log 5

      // Parse the entire response body into FinancesResponseModel
      return FinancesResponseModel.fromJson(decodedResponse);
    } on DioException catch (e) {
      debugPrint('FinancesRemoteDataSourceImpl: DioException response: ${e.response}');
      final decodedResponse = latin1.decode(e.response!.data);
      final response = jsonDecode(decodedResponse);
      debugPrint('FinancesRemoteDataSourceImpl: getPaidFees --> Decoded response: $response');
      debugPrint('FinancesRemoteDataSourceImpl: getPaidFees --> is reponse map: ${response is Map<String, dynamic>}');
      if (response != null && response is Map<String, dynamic>) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          response,
          e.response!.statusCode
        );
        throw ServerException(apiException.getUserMessage());
      } else if (response != null && response is List) {
        throw ServerException('Erreur de format de réponse inattendue lors de la récupération des frais payés. Veuillez réessayer plus tard.');
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion lors de la récupération des frais payés: ${e.message}');
      }
    } catch (e) {
      debugPrint('FinancesRemoteDataSourceImpl: Exception: $e');
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération des frais payés: $e');
    }
  }

// ========================================== getFilteredPaidFees ====================================================
  @override
  Future<FinancesResponseModel> getFilteredPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String dateDebut,
    required String dateFin,
    String? codeUtilisateur,
  }) async {
    try {
      debugPrint('FinancesRemoteDataSourceImpl: Calling getFilteredPaidFees with params: $codeEtab, $telephone, $codeEtudiant, $dateDebut, $dateFin, $codeUtilisateur');

      // Make HTTP GET request to the fraisPayesFiltres endpoint with query parameters
      final queryParams = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
        'dateDebut': dateDebut,
        'dateFin': dateFin,
      };

      if (codeUtilisateur != null) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }

      final response = await apiClient.getWithToken(
        ApiEndpoints.fraisPayesFiltres,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes),
      );

      debugPrint('FinancesRemoteDataSourceImpl: Received filtered response with status code: ${response.statusCode}');
      debugPrint('FinancesRemoteDataSourceImpl: Raw filtered response data: ${response.data}');

      final String responseJsonString = latin1.decode(response.data);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded filtered response string: $responseJsonString');

      final decodedResponse = jsonDecode(responseJsonString);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded filtered JSON object: $decodedResponse');

      // Parse the entire response body into FinancesResponseModel
      return FinancesResponseModel.fromJson(decodedResponse);
    } catch (e) {
      debugPrint("FinancesRemoteDataSourceImpl: Failed to get filtered paid fees: $e");
      throw Exception('Failed to get filtered paid fees: $e');
    }
  }


// ========================================== getUnpaidFees ====================================================
  @override
  Future<FinancesUnpaidResponseModel> getUnpaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Make HTTP GET request to the fraisImpayes endpoint with query parameters
      final queryParams = {
        'codeEtab': codeEtab,
        'telephone': telephone.replaceAll("+", ""),
        'codeEtudiant': codeEtudiant,
      };
      if (codeUtilisateur != null) {
        queryParams['codeUtilisateur'] = codeUtilisateur;
      }
      final response = await apiClient.getWithToken(
        ApiEndpoints.fraisImpayes,
        queryParameters: queryParams,
        options: Options(responseType: ResponseType.bytes),
      );
      debugPrint('FinancesRemoteDataSourceImpl: Received response with status code: ${response.statusCode}'); // Log 2
      final responseJsonString = latin1.decode(response.data);
      final decodedResponse = jsonDecode(responseJsonString);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded JSON object: $decodedResponse'); // Log 5
      // Parse the entire response body into FinancesUnpaidResponseModel
      return FinancesUnpaidResponseModel.fromJson(decodedResponse);
    } on DioException catch (e) {
      debugPrint('FinancesRemoteDataSourceImpl: DioException: $e');
      debugPrint('FinancesRemoteDataSourceImpl: DioException response status: ${e.response!.statusCode}');

      final decodedResponse = latin1.decode(e.response!.data);
      final response = jsonDecode(decodedResponse);
      debugPrint('FinancesRemoteDataSourceImpl: Decoded response: $response');
      if (response != null) {
        final apiException = ApiException.fromJson(
          response,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        throw NetworkException('Erreur de connexion lors de la récupération de la carte virtuelle: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de la récupération de la carte virtuelle: $e');
    }
  }
  
}
