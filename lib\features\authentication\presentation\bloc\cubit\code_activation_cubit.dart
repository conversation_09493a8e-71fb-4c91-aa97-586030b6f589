import 'package:kairos/core/error/failures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/verify_pin_usecase.dart'; // Import the use case
import '../state/auth_state.dart';

/// Cubit for managing code activation state
class CodeActivationCubit extends Cubit<AuthState> {
  final VerifyPinUseCase verifyPinUseCase; // Add use case dependency

  CodeActivationCubit({required this.verifyPinUseCase}) : super(const AuthInitial()); // Update constructor

  /// Verify the activation PIN and user's full name
  ///
  /// [fullName] - The user's full name
  /// [otp] - The entered OTP/PIN
  Future<void> verifyPin(String fullName, String otp, String phoneNumber) async {
    emit(const CodeActivationLoadingState());

    final result = await verifyPinUseCase(fullName, otp, phoneNumber); // Call the use case

    result.fold(
      (failure) {
        // Handle failure
        if (failure is ServerFailure) {
          // Access returnCode directly from ServerFailure
          emit(CodeActivationErrorState(
            returnCode: failure.returnCode,
            userMessage: failure.message, // message is the userMessage
          ));
        } else {
          // Handle other failure types
          emit(CodeActivationErrorState(userMessage: failure.message));
        }
      },
      (success) {
        // Handle success
        emit(const CodeActivationSuccessState());
      },
    );
  }
}