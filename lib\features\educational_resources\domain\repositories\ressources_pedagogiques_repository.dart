import 'package:dartz/dartz.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';

/// Repository interface for educational resources
abstract class RessourcesPedagogiquesRepository {
  /// Fetches all educational resources for a student
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> getRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Fetches educational resources filtered by date range
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> getRessourcesPedagogiquesFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? dateDebut,
    String? dateFin,
  });

  /// Fetches attachment for a specific educational resource
  Future<Either<Failure, DossierAttachmentEntity>> getRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  });
}
