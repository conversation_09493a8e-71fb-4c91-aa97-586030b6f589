

import 'package:flutter/material.dart';

class CustomSnackbar { 
  
  const CustomSnackbar({required this.message, this.isError = false});
  final String message;
  final bool isError;

  SnackBar getSnackBar(){
    return SnackBar(
      content: Text(message, textAlign: TextAlign.center),
      width: 300,
      duration: const Duration(seconds: 4),
      shape: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(20.0)), 
                    borderSide: BorderSide.none),
      behavior: SnackBarBehavior.floating,
      backgroundColor: isError ? Colors.redAccent : null, // Set background color based on isError
    );
  }
}