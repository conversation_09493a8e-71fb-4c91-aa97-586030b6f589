import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user.dart';
import '../entities/sms_response_entity.dart';
import '../entities/password_reset_entity.dart'; // Import the new entity
import '../../data/models/reset_password_result.dart';

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();
  
  /// Get current user
  Future<Either<Failure, User?>> getCurrentUser();
  
  /// Send SMS for verification
  Future<Either<Failure, SmsResponseEntity>> sendSms(String phoneNumber); // Update return type
  
  /// Refresh authentication token
  Future<Either<Failure, String>> refreshToken();

  /// Resend SMS for verification
  Future<Either<Failure, dynamic>> resendSms(String phoneNumber);

  /// Verify PIN code with full name and device info
  Future<Either<Failure, dynamic>> verifyPinWithDetails(String fullName, String otp, String phoneNumber);

  /// Check the user's response to the reactivation dialog
  Future<Either<Failure, dynamic>> checkResponse(String phoneNumber, {required bool activated});

  /// Send password reset email
  Future<Either<Failure, PasswordResetResponseEntity>> sendPasswordResetEmail(String email, String codeEtab);

  /// Reset password with PIN and new password
  Future<Either<Failure, ResetPasswordResult>> resetPassword(
    String codeEtab,
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  );
}
