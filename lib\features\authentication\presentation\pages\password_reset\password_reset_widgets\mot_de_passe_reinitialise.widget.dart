import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/core/utils/navigation_utils.dart';

class MotDePasseReinitialiseWidget extends StatefulWidget {
  const MotDePasseReinitialiseWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<MotDePasseReinitialiseWidget> createState() => _MotDePasseReinitialiseWidgetState();
}

class _MotDePasseReinitialiseWidgetState extends State<MotDePasseReinitialiseWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text(
          "FÉLICITATIONS!",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        SizedBox(
          height: 20,
          width: 200,
          child: Divider(
            color: Theme.of(context).primaryColor,
            thickness: 5,
          ),
        ),
        const Spacer(),
        SvgPicture.asset("assets/images/illustration_felicitation.svg"),
        const Spacer(flex: 2),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text(
            "Votre mot de passe a été réinitialisé avec succès !",
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 20),
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            fixedSize: WidgetStateProperty.all(const Size(300, 50)),
          ),
          onPressed: () {
            debugPrint('User clicked on Continue button');
            NavigationUtils.popUntil(context, ModalRoute.withName("/activate_school"));
          },
          child: const Text(
            "CRÉER UN NOUVEL ÉTABLISSEMENT",
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        const SizedBox(height: 20),
        const Spacer(),
      ],
    );
  }
}
