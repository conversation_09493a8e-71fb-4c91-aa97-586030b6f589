import 'package:kairos/core/device_info.dart';

class DeconnexionRequest extends DeviceInfo {
  final String codeUtilisateur;
  final String codeEtab;
  DeconnexionRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required this.codeUtilisateur,
    required this.codeEtab,
  });

  factory DeconnexionRequest.fromJson(Map<String, dynamic> json) {
    return DeconnexionRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      codeUtilisateur: json['codeUtilisateur'],
      codeEtab: json['codeEtab'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'codeUtilisateur': codeUtilisateur,
      'codeEtab': codeEtab,
      'numeroTelephone': numeroTelephone,
    });
    return json;
  }
}
