import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/core/usecases/usecase.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:kairos/features/student_records/domain/repositories/dossier_repository.dart';

class GetDossiersUseCase implements UseCase<List<DossierEntity>, GetDossiersParams> {
  final DossierRepository repository;

  GetDossiersUseCase(this.repository);

  @override
  Future<Either<Failure, List<DossierEntity>>> call(
      GetDossiersParams params) async {
    return await repository.getDossiers(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

class GetDossiersParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetDossiersParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props =>
      [codeEtab, telephone, codeEtudiant, codeUtilisateur];
}