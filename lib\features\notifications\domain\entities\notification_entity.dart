import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

/// Unified notification entity for both SMS and email notifications
class NotificationEntity extends Equatable {
  final int idObject;
  final String content;
  final DateTime dateCreation;
  final bool isSent;
  final NotificationTypeEntity type;
  final String? telephone; // For SMS notifications
  final String? email; // For email notifications
  final String? subject; // For email notifications only
  final String? body; // For email notifications only

  const NotificationEntity({
    required this.idObject,
    required this.content,
    required this.dateCreation,
    required this.isSent,
    required this.type,
    this.telephone,
    this.email,
    this.subject,
    this.body,
  });

  /// Get the sender information based on notification type
  String get sender {
    switch (type) {
      case NotificationTypeEntity.sms:
        return telephone ?? 'Unknown';
      case NotificationTypeEntity.email:
        return email ?? 'Unknown';
    }
  }

  /// Get the formatted creation date
  String get formattedDate {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateCreation);
  }

  /// Get the title based on notification type
  String get title {
    switch (type) {
      case NotificationTypeEntity.sms:
        return 'SMS';
      case NotificationTypeEntity.email:
        return subject ?? 'Email';
    }
  }

  /// Get the main content based on notification type
  String get mainContent {
    switch (type) {
      case NotificationTypeEntity.sms:
        return content;
      case NotificationTypeEntity.email:
        return body ?? content;
    }
  }

  @override
  List<Object?> get props => [
        idObject,
        content,
        dateCreation,
        isSent,
        type,
        telephone,
        email,
        subject,
        body,
      ];
}

/// Notification type enumeration
enum NotificationTypeEntity {
  sms,
  email,
}
