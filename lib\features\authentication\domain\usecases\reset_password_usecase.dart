import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';
import 'package:kairos/features/authentication/data/models/reset_password_result.dart';

/// Use case for resetting password
class ResetPasswordUseCase {
  final AuthRepository repository;

  ResetPasswordUseCase(this.repository);

  /// Execute the reset password use case
  ///
  /// [email] - The user's email address
  /// [pin] - The verification PIN
  /// [newPassword] - The new password
  /// [confirmPassword] - The password confirmation
  /// Returns [Either<Failure, PasswordResetResponseEntity>] - Success or failure result
  Future<Either<Failure, ResetPasswordResult>> call(
    String codeEtab,
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  ) async {
    return await repository.resetPassword(codeEtab, email, pin, newPassword, confirmPassword);
  }
}
