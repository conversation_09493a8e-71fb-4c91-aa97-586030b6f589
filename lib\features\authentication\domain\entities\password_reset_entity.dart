import 'package:equatable/equatable.dart';

/// Entity representing a password reset request
class PasswordResetRequestEntity extends Equatable {
  final String email;

  const PasswordResetRequestEntity({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

/// Entity representing a password reset confirmation
class PasswordResetConfirmEntity extends Equatable {
  final String email;
  final String pin;
  final String newPassword;
  final String confirmPassword;

  const PasswordResetConfirmEntity({
    required this.email,
    required this.pin,
    required this.newPassword,
    required this.confirmPassword,
  });

  @override
  List<Object?> get props => [email, pin, newPassword, confirmPassword];
}

/// Entity representing a password reset response
class PasswordResetResponseEntity extends Equatable {
  final bool status;
  final String? user;
  final String? email;
  final String? tokenValue;
  final String? message;

  const PasswordResetResponseEntity({
    required this.status,
    this.user,
    this.email,
    this.tokenValue,
    this.message,
  });

  @override
  List<Object?> get props => [status, user, email, tokenValue, message];
}
