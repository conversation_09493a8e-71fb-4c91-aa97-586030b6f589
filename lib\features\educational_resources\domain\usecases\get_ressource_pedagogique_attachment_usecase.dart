import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/core/usecases/usecase.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';

/// Use case for fetching educational resource attachment
class GetRessourcePedagogiqueAttachmentUseCase implements UseCase<DossierAttachmentEntity, GetRessourcePedagogiqueAttachmentParams> {
  final RessourcesPedagogiquesRepository repository;

  GetRessourcePedagogiqueAttachmentUseCase(this.repository);

  @override
  Future<Either<Failure, DossierAttachmentEntity>> call(
      GetRessourcePedagogiqueAttachmentParams params) async {
    return await repository.getRessourcePedagogiqueAttachment(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      idObject: params.idObject,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetRessourcePedagogiqueAttachmentUseCase
class GetRessourcePedagogiqueAttachmentParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final int idObject;
  final String? codeUtilisateur;

  const GetRessourcePedagogiqueAttachmentParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    required this.idObject,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, idObject, codeUtilisateur];
}
