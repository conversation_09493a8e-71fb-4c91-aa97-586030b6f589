import 'package:equatable/equatable.dart';

/// Base state for course content entry
abstract class CourseContentEntryState extends Equatable {
  const CourseContentEntryState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class CourseContentEntryInitial extends CourseContentEntryState {
  const CourseContentEntryInitial();
}

/// Loading state
class CourseContentEntryLoading extends CourseContentEntryState {
  const CourseContentEntryLoading();
}

/// Success state
class CourseContentEntrySuccess extends CourseContentEntryState {
  final String message;

  const CourseContentEntrySuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

/// Error state
class CourseContentEntryError extends CourseContentEntryState {
  final String message;

  const CourseContentEntryError({required this.message});

  @override
  List<Object?> get props => [message];
}
