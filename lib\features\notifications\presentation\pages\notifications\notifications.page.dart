import 'package:kairos/core/enums/header_enums.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart'; // Added import
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/core/di/injection_container.dart';
import 'package:kairos/features/notifications/presentation/bloc/notifications_cubit.dart';
import 'package:kairos/features/notifications/presentation/bloc/notifications_state.dart';
import 'package:kairos/features/notifications/presentation/pages/notifications/notifications_widgets/notification_tile.widget.dart';
import 'package:kairos/features/notifications/domain/entities/notification_entity.dart'; // Added import

class NotificationsPage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant;

  const NotificationsPage({
    super.key,
    required this.school,
    this.etudiant,
  });

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  List<NotificationEntity> _filteredNotifications = [];
  List<NotificationEntity> _allNotifications = [];

  late AnimationController _searchAnimationController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _searchController.addListener(_filterNotifications);
    _loadNotifications();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  void _loadNotifications() async {
    final notificationsCubit = context.read<NotificationsCubit>();
    final authLocalDataSource = sl<AuthLocalDataSource>();

    // Get phone number from SharedPreferences
    final phoneNumber = await authLocalDataSource.getPhoneNumber();

    if (phoneNumber != null) {
      // Get student code - use etudiant if available, otherwise use school.codeUtilisateur
      final codeEtudiant = widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur;

      // Load notifications
      notificationsCubit.loadNotificationsData(
        codeEtab: widget.school.codeEtab,
        telephone: phoneNumber,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: widget.school.codeUtilisateur,
      );
    }
  }

  String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Method to filter notifications based on search query
  void _filterNotifications() {
    final String query = _searchController.text.toLowerCase();
    setState(() {
      _filteredNotifications = _allNotifications.where((notification) {
        return notification.mainContent.toLowerCase().contains(query) ||
               notification.title.toLowerCase().contains(query) ||
               notification.sender.toLowerCase().contains(query) ||
               formatDate(notification.dateCreation).toLowerCase().contains(query);
      }).toList();
    });
  }

  /// Method to toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear();
        _filterNotifications();
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            isSearchBarVisible: _isSearchBarVisible,
            onSearchTap: _toggleSearchBarVisibility,
            pageSection: HeaderEnum.notifications,
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
            title: "NOTIFICATIONS",
          ),
          // Search bar
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0,
                  searchController: _searchController,
                  showDateFilter: false,
                  onSearchChanged: (query) => _filterNotifications(),
                  onDateFilterChanged: (dateRange) {}, // Not used for notifications
                  onClearDateFilter: () {}, // Not used for notifications
                  hasActiveFilter: false, // Not used for notifications
                  hintText: "Rechercher notifications...",
                ),
                pinned: true,
              );
            }
          ),
          SliverToBoxAdapter(
            child: SizedBox(height: 20),
          ),
          BlocBuilder<NotificationsCubit, NotificationsState>(
            builder: (context, state) {
              if (state is NotificationsLoading) {
                return SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: CustomSpinner(),
                    ),
                  ),
                );
              } else if (state is NotificationsError) {
                return SliverToBoxAdapter(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Erreur lors du chargement des notifications',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 8),
                          Text(
                            state.message,
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _loadNotifications,
                            child: Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              } else if (state is NotificationsLoaded) {
                _allNotifications = state.notifications;
                if (_filteredNotifications.isEmpty && _searchController.text.isEmpty) {
                  _filteredNotifications = _allNotifications;
                }

                if (_filteredNotifications.isEmpty) {
                  return SliverToBoxAdapter(
                    child: EmptyMessage(
                      message: 'Aucune notification disponible',
                    ),
                  );
                }

                return SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final notification = _filteredNotifications[index];
                      final formattedDate = formatDate(notification.dateCreation);
                      return NotificationTile(
                        notification: notification,
                        formattedDate: formattedDate,
                      );
                    },
                    childCount: _filteredNotifications.length,
                  ),
                );
              }

              return SliverToBoxAdapter(
                child: SizedBox.shrink(),
              );
            },
          ),
        ],
      ),
    );
  }
}
