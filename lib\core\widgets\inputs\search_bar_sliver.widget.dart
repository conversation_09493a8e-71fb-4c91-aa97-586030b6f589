import 'package:kairos/core/widgets/dialogs/date_filter_dialog.widget.dart';
import 'package:kairos/core/utils/date_utils.dart' as du;
import 'package:flutter/material.dart';

class SearchBarSliver extends SliverPersistentHeaderDelegate {
  final double extentHeight;
  final TextEditingController searchController;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<Map<String, String>>? onDateFilterChanged;
  final VoidCallback? onClearDateFilter;
  final String? hintText;
  final bool showYear;
  final bool hasActiveFilter;
  final bool showDateFilter;
  final String? startDate;
  final String? endDate;

  SearchBarSliver({
    required this.extentHeight,
    required this.searchController,
    this.hintText,
    this.onSearchChanged,
    this.onDateFilterChanged,
    this.onClearDateFilter,
    this.showYear = false,
    this.hasActiveFilter = false,
    this.showDateFilter = true,
    this.startDate,
    this.endDate,
  });

  @override
  double get maxExtent => extentHeight;

  @override
  double get minExtent => extentHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      height: maxExtent,
      child: Padding(
        padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 8.0, bottom: 0.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search field with flexible height
            Flexible(
              flex: hasActiveFilter ? 6 : 10,
              child: Material(
                color: Colors.transparent,
                child: TextField(
                  controller: searchController,
                  onChanged: onSearchChanged,
                  decoration: InputDecoration(
                    hintText: hintText ?? "Rechercher...",
                    isDense: true,
                    suffixIcon: showDateFilter ? IconButton(
                      iconSize: 22 * (extentHeight / (hasActiveFilter? 88.0: 60.0)),
                      onPressed: () async {
                        final result = await showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return DateFilterDialog(showYear: showYear);
                          },
                        );
                        debugPrint("result: $result");

                        // Validate and process the result
                        if (result != null && result is Map<String, dynamic>) {
                          final String? startDate = result['startDate'] as String?;
                          final String? endDate = result['endDate'] as String?;
                          debugPrint("startDate: $startDate, endDate: $endDate");
                          // Validate that both dates are present and not empty
                          if (startDate != null && startDate.isNotEmpty &&
                              endDate != null && endDate.isNotEmpty) {
                            // Call the date filter callback with validated dates
                            if (onDateFilterChanged != null) {
                              onDateFilterChanged!({
                                'startDate': startDate,
                                'endDate': endDate,
                              });
                            }
                          } else if (endDate != null && endDate.isNotEmpty && showYear) {
                            // Call the date filter callback with validated dates
                            debugPrint("endDate --->: $endDate");
                            if (onDateFilterChanged != null) {
                              onDateFilterChanged!({
                                'endDate': endDate,
                              });
                            }
                          } else {
                            // Show error message if dates are missing
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Veuillez sélectionner les deux dates pour filtrer'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        }
                      },
                      icon: Icon(Icons.calendar_month_sharp, color: Colors.grey[600]),
                    ) : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                      borderSide: BorderSide(color: Colors.grey[300]!, width: 1.0),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(vertical: 0.0, horizontal: 16.0),
                  ),
                ),
              ),
            ),
            // Animated chip for active date filter with flexible height
            if (hasActiveFilter)
              Flexible(
                flex: 4,
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 200),
                  opacity: hasActiveFilter ? 1.0 : 0.0,
                  child: _buildDateFilterChip(),
                ),
              ),
          ],
        ),
      ),
    );
  } 

  Widget _buildDateFilterChip() {
    String chipText = '';
    debugPrint('showYear ---> : $showYear, startDate: $startDate, endDate: $endDate');
    if (showYear && endDate != null) {
      // For year-only filtering
      debugPrint("endDate: $endDate");
      chipText = endDate!;
    } else if (startDate != null && endDate != null) {
      // For date range filtering
      try {
        // Parse the dates and format them
        final formattedStartDate = du.formatDateFr(startDate!);
        final formattedEndDate = du.formatDateFr(endDate!);
        chipText = '$formattedStartDate - $formattedEndDate';
      } catch (e) {
        // Fallback: try to format as dd/MM/yyyy if already in that format
        debugPrint("Error formatting dates: $e");
        try {
          // Check if dates are already in dd/MM/yyyy format
          if (startDate!.contains('/') && endDate!.contains('/')) {
            chipText = '$startDate - $endDate';
          } else {
            // Try parsing as DateTime and formatting manually
            final startDateTime = DateTime.parse(startDate!);
            final endDateTime = DateTime.parse(endDate!);
            final startFormatted = '${startDateTime.day.toString().padLeft(2, '0')}/${startDateTime.month.toString().padLeft(2, '0')}/${startDateTime.year}';
            final endFormatted = '${endDateTime.day.toString().padLeft(2, '0')}/${endDateTime.month.toString().padLeft(2, '0')}/${endDateTime.year}';
            chipText = '$startFormatted - $endFormatted';
          }
        } catch (e2) {
          // Final fallback to original strings
          debugPrint("Final fallback for date formatting: $e2");
          chipText = '$startDate - $endDate';
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Align(
        alignment: Alignment.centerRight,
        child: Chip(
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: VisualDensity.compact,
          label: Text(
            chipText,
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          deleteIcon: const Icon(
            Icons.close,
            size: 14,
          ),
          onDeleted: onClearDateFilter,
          backgroundColor: Colors.blue.shade50,
          deleteIconColor: Colors.red.shade600,
          side: BorderSide(
            color: Colors.blue.shade200,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  
  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    // Rebuild if max/min extent changes or if search controller/callback changes
    final SearchBarSliver oldSearchBarSliver = oldDelegate as SearchBarSliver;
    return oldDelegate.maxExtent != maxExtent ||
        oldDelegate.minExtent != minExtent ||
        oldSearchBarSliver.hintText != hintText ||
        oldSearchBarSliver.searchController != searchController ||
        oldSearchBarSliver.onSearchChanged != onSearchChanged ||
        oldSearchBarSliver.onDateFilterChanged != onDateFilterChanged ||
        oldSearchBarSliver.onClearDateFilter != onClearDateFilter ||
        oldSearchBarSliver.hasActiveFilter != hasActiveFilter ||
        oldSearchBarSliver.startDate != startDate ||
        oldSearchBarSliver.endDate != endDate;
  }

}