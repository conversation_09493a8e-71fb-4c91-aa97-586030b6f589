import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/get_notes_evaluations_usecase.dart';
import '../../domain/usecases/get_filtered_notes_evaluations_usecase.dart';
import 'notes_state.dart';

/// Notes Cubit for managing notes state
class NotesCubit extends Cubit<NotesState> {
  final GetNotesEvaluationsUseCase getNotesEvaluationsUseCase;
  final GetFilteredNotesEvaluationsUseCase getFilteredNotesEvaluationsUseCase;

  NotesCubit({
    required this.getNotesEvaluationsUseCase,
    required this.getFilteredNotesEvaluationsUseCase,
  }) : super(const NotesInitial());
  
  /// Load notes evaluations data
  Future<void> loadNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const NotesLoading());
    
    try {
      final params = GetNotesEvaluationsParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      
      final result = await getNotesEvaluationsUseCase.call(params);
      
      result.fold(
        (failure) => emit(NotesError(failure.message)),
        (notesEvaluations) => emit(NotesLoaded(notesEvaluations: notesEvaluations)),
      );
    } catch (e) {
      emit(NotesError('Une erreur inattendue s\'est produite: $e'));
    }
  }
  
  /// Load filtered notes evaluations data by date range
  Future<void> loadFilteredNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required String dateDebut,
    required String dateFin,
  }) async {
    emit(const NotesLoading());

    try {
      final params = GetFilteredNotesEvaluationsParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        dateDebut: dateDebut,
        dateFin: dateFin,
      );

      final result = await getFilteredNotesEvaluationsUseCase.call(params);

      result.fold(
        (failure) => emit(NotesError(failure.message)),
        (notesEvaluations) => emit(NotesLoaded(notesEvaluations: notesEvaluations)),
      );
    } catch (e) {
      emit(NotesError('Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Refresh notes data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadNotesEvaluations(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }
}
