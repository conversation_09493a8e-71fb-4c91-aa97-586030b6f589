import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Utility method to check if a date is within the specified range.
bool isDateInRange(String itemDate, String startDate, String endDate) {
  try {
    DateTime item = parseDate(itemDate);
    DateTime start = parseDate(startDate);
    DateTime end = parseDate(endDate);

    return item.isAfter(start.subtract(const Duration(days: 1))) &&
           item.isBefore(end.add(const Duration(days: 1)));
  } catch (e) {
    debugPrint("Error parsing dates: $e");
    return true; // If parsing fails, include the item
  }
}

/// Utility method to parse a date string in various formats.
DateTime parseDate(String dateStr) {
  if (dateStr.contains('/')) {
    List<String> parts = dateStr.split('/');
    if (parts.length == 3) {
      // Format: dd/MM/yyyy
      return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
    } else if (parts.length == 2) {
      // Format: dd/MM - add current year
      int currentYear = DateTime.now().year;
      return DateTime(currentYear, int.parse(parts[1]), int.parse(parts[0]));
    }
  } else if (dateStr.contains('-')) {
    // Format: dd-MM-yyyy
    List<String> parts = dateStr.split('-');
    if (parts.length == 3) {
      return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
    }
    throw FormatException("Unable to parse date: $dateStr");
  }
  throw FormatException("Unable to parse date: $dateStr");
}

/// Format date for display as header (e.g., "2024-01-15" -> "Lundi 15 Janvier 2024")
String formatDateHeader(String dateStr) {
  try {
    DateTime date = parseDate(dateStr);

    // French day names
    const List<String> dayNames = [
      'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'
    ];

    // French month names
    const List<String> monthNames = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    String dayName = dayNames[date.weekday - 1];
    String monthName = monthNames[date.month - 1];

    return '$dayName ${date.day} $monthName ${date.year}';
  } catch (e) {
    debugPrint("Error formatting date header: $e");
    return dateStr; // Return original string if parsing fails
  }
}



String formatDateFr(String dateString){
  return DateFormat(AppConstants.dateFormat).format(DateTime.parse(dateString));
}