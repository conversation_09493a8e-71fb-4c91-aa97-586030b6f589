import 'package:equatable/equatable.dart';

/// Domain entity for course log (cahier texte) items
class CourseLogEntity extends Equatable {
  final String dateCours;
  final String heureDebutPrevu;
  final String heureFinPrevu;
  final String heureDebutSaisi;
  final String heureFinSaisi;
  final String dureeSaisi;
  final String cours;
  final String professeur;
  final String semestre;
  final String auteurSaisi;
  final String etudiant;
  final String classe;
  final String contenuSaisi;
  final int idObject;
  final String matriculeProfesseur;
  final int idPieceJointe;
  final String? responsableUsername; 

  const CourseLogEntity({
    required this.dateCours,
    required this.heureDebutPrevu,
    required this.heureFinPrevu,
    required this.heureDebutSaisi,
    required this.heureFinSaisi,
    required this.dureeSaisi,
    required this.cours,
    required this.etudiant,
    required this.professeur,
    required this.semestre,
    required this.auteurSaisi,
    required this.classe,
    required this.contenuSaisi,
    required this.idObject,
    required this.matriculeProfesseur,
    required this.idPieceJointe,
    this.responsableUsername, 
  });

  @override
  List<Object?> get props => [
        dateCours,
        heureDebutPrevu,
        heureFinPrevu,
        heureDebutSaisi,
        heureFinSaisi,
        dureeSaisi,
        cours,
        professeur,
        etudiant,
        semestre,
        auteurSaisi,
        classe,
        contenuSaisi,
        idObject,
        matriculeProfesseur,
        idPieceJointe,
        responsableUsername, // Add to props list
      ];

  // Factory method to create a CourseLogEntity from a JSON map
  factory CourseLogEntity.fromJson(Map<String, dynamic> json) {
    return CourseLogEntity(
      dateCours: json['dateCours'] as String,
      heureDebutPrevu: json['heureDebutPrevu'] as String,
      heureFinPrevu: json['heureFinPrevu'] as String,
      heureDebutSaisi: json['heureDebutSaisi'] as String,
      heureFinSaisi: json['heureFinSaisi'] as String,
      dureeSaisi: json['dureeSaisi'] as String,
      cours: json['cours'] as String,
      professeur: json['professeur'] as String,
      etudiant: json['etudiant'] as String,
      semestre: json['semestre'] as String,
      auteurSaisi: json['auteurSaisi'] as String,
      classe: json['classe'] as String,
      contenuSaisi: json['contenuSaisi'] as String,
      idObject: json['idObject'] as int,
      matriculeProfesseur: json['matriculeProfesseur'] as String,
      idPieceJointe: json['idPieceJointe'] as int,
      responsableUsername: json['responsableUsername'] as String?, // Parse responsableUsername
    );
  }

  // Method to serialize CourseLogEntity to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'dateCours': dateCours,
      'heureDebutPrevu': heureDebutPrevu,
      'heureFinPrevu': heureFinPrevu,
      'heureDebutSaisi': heureDebutSaisi,
      'heureFinSaisi': heureFinSaisi,
      'dureeSaisi': dureeSaisi,
      'cours': cours,
      'etudiant': etudiant,
      'professeur': professeur,
      'semestre': semestre,
      'auteurSaisi': auteurSaisi,
      'classe': classe,
      'contenuSaisi': contenuSaisi,
      'idObject': idObject,
      'matriculeProfesseur': matriculeProfesseur,
      'idPieceJointe': idPieceJointe,
      'responsableUsername': responsableUsername, // Serialize responsableUsername
    };
  }
}