import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/course_content_entry_cubit.dart';
import '../../bloc/course_content_entry_state.dart';
import '../../../../../core/widgets/common/snackbar_widget.dart';
import '../../../../../features/authentication/data/datasources/auth_local_datasource.dart';
import '../../../../../core/di/injection_container.dart';
import 'package:kairos/features/schedule/domain/entities/emploi_temps_entity.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import '../../../../../core/error/exceptions.dart'; // Import ServerException
import '../../../../../core/widgets/dialogs/custom_error_dialog.widget.dart'; // Import CustomErrorDialog


class CourseContentEntryForm extends StatefulWidget {
  final EmploiTempsEntity scheduleItem;
  final Function(String courseName, String teacherName, String timeRange) onSuccess;
  final VoidCallback onCancel;
  final String codeEtab;
  final String codeEtudiant;
  final EtablissementUtilisateur currentUser;

  const CourseContentEntryForm({
    super.key,
    required this.scheduleItem,
    required this.onSuccess,
    required this.currentUser,
    required this.onCancel,
    required this.codeEtab,
    required this.codeEtudiant,
  });

  @override
  State<CourseContentEntryForm> createState() => _CourseContentEntryFormState();
}

// State for the CourseContentEntryForm widget
class _CourseContentEntryFormState extends State<CourseContentEntryForm> {
  // Controllers for the time input fields
  late TextEditingController _heureDebutController;
  late TextEditingController _heureFinController;
  late TextEditingController _observationsController;

  // State variables to store parsed and selected times
  TimeOfDay? _parsedHeureDebut;
  TimeOfDay? _parsedHeureFin;
  TimeOfDay? _selectedHeureDebut;
  TimeOfDay? _selectedHeureFin;

  late AuthLocalDataSource _authLocalDataSource;

  @override
  void initState() {
    super.initState();
    _authLocalDataSource = sl<AuthLocalDataSource>();

    // Parse the initial time range from scheduleItem.heure
    // For testing, using a placeholder string "08:00-17:00" if scheduleItem.heure is empty
    final String initialTimeRange = widget.scheduleItem.heure.isNotEmpty
        ? widget.scheduleItem.heure
        : "08:00-17:00"; // Placeholder for testing
    final List<TimeOfDay?> parsedTimes = _parseTimeRange(initialTimeRange);
    _parsedHeureDebut = parsedTimes[0];
    _parsedHeureFin = parsedTimes[1];

    // Initialize selected times with parsed values
    _selectedHeureDebut = _parsedHeureDebut;
    _selectedHeureFin = _parsedHeureFin;

    // Initialize controllers with formatted selected times
    _heureDebutController = TextEditingController(
      text: _selectedHeureDebut != null
          ? _formatTimeOfDay(_selectedHeureDebut!)
          : '',
    );
    _heureFinController = TextEditingController(
      text: _selectedHeureFin != null
          ? _formatTimeOfDay(_selectedHeureFin!)
          : '',
    );
    _observationsController = TextEditingController();
  }

  /// Parses a time range string (e.g., "HH:MM-HH:MM") into two TimeOfDay objects.
  /// Returns a list containing [startTime, endTime].
  List<TimeOfDay?> _parseTimeRange(String timeRange) {
    try {
      final parts = timeRange.split('-');
      if (parts.length == 2) {
        final startTimeParts = parts[0].trim().split(':');
        final endTimeParts = parts[1].trim().split(':');

        if (startTimeParts.length == 2 && endTimeParts.length == 2) {
          final int startHour = int.parse(startTimeParts[0]);
          final int startMinute = int.parse(startTimeParts[1]);
          int endHour =  int.parse(endTimeParts[0]);
          final int endMinute = int.parse(endTimeParts[1]);
          
          if(startHour > endHour && endHour < 12){
              endHour += 12;
          }

          return [
            TimeOfDay(hour: startHour, minute: startMinute),
            TimeOfDay(hour: endHour, minute: endMinute),
          ];
        }
      }
    } catch (e) {
      debugPrint('Error parsing time range "$timeRange": $e');
    }
    return [null, null]; // Return nulls if parsing fails
  }

  /// Formats a TimeOfDay object into a "HH:MM" string.
  String _formatTimeOfDay(TimeOfDay time) {
    final String hour = time.hour.toString().padLeft(2, '0');
    final String minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  @override
  void dispose() {
    _heureDebutController.dispose();
    _heureFinController.dispose();
    _observationsController.dispose();
    super.dispose();
  }

  /// Shows a time picker dialog and updates the corresponding time controller and state variable.
  /// Includes validation logic based on parsed and selected times.
  Future<void> _pickTime(BuildContext context, {required bool isStartTime}) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? (_selectedHeureDebut ?? TimeOfDay.now())
          : (_selectedHeureFin ?? _selectedHeureDebut ?? TimeOfDay.now()),
    );

    if (pickedTime != null) {
      setState(() {
        if (isStartTime) {
          _selectedHeureDebut = pickedTime;
          _heureDebutController.text = _formatTimeOfDay(pickedTime);
        } else {
          _selectedHeureFin = pickedTime;
          _heureFinController.text = _formatTimeOfDay(pickedTime);
        }
      });
    }
  }

  /// Compares two TimeOfDay objects to check if time1 is before time2.
  bool _isTimeBefore(TimeOfDay time1, TimeOfDay time2) {
    if (time1.hour < time2.hour) {
      return true;
    }
    if (time1.hour == time2.hour && time1.minute < time2.minute) {
      return true;
    }
    return false;
  }

  Future<void> _submitForm() async {
    // Validate the form fields before submission
    if (_heureDebutController.text.isEmpty || _heureFinController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "Veuillez sélectionner les heures de début et de fin",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    // Additional validation based on TimeOfDay objects
    if (_parsedHeureDebut != null && _selectedHeureDebut != null &&
        _isTimeBefore(_selectedHeureDebut!, _parsedHeureDebut!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "L'heure de début ne peut pas être antérieure à l'heure de début du cours",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    if (_parsedHeureDebut != null && _selectedHeureFin != null &&
        _isTimeBefore(_selectedHeureFin!, _parsedHeureDebut!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "L'heure de fin ne peut pas être antérieure à l'heure de début du cours",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    if (_selectedHeureDebut != null && _selectedHeureFin != null &&
        _isTimeBefore(_selectedHeureFin!, _selectedHeureDebut!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "L'heure de fin ne peut pas être antérieure à l'heure de début sélectionnée",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    if (_observationsController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        CustomSnackbar(
          message: "Veuillez saisir les observations",
          isError: true,
        ).getSnackBar(),
      );
      return;
    }

    try {
      // Get device info and user data
      final deviceInfo = await _authLocalDataSource.getCachedDeviceInfo();
      final phoneNumber = await _authLocalDataSource.getPhoneNumber() ?? '';
      
      // Submit the form
      if (context.mounted) {
        context.read<CourseContentEntryCubit>().submitCourseContent(
          codeEtudiant: widget.codeEtudiant,
          codeEtab: widget.codeEtab,
          heureDebut: _heureDebutController.text,
          heureFin: _heureFinController.text,
          dateCours: widget.scheduleItem.date,
          emploiDuTempsId: widget.scheduleItem.emploiDuTempsId ?? 0,
          responsableId: widget.scheduleItem.responsableId ?? 0,
          observations: _observationsController.text,
          numeroTelephone: phoneNumber,
          marqueTelephone: deviceInfo?['marqueTelephone'] ?? '',
          modelTelephone: deviceInfo?['modelTelephone'] ?? '',
          imeiTelephone: deviceInfo?['imeiTelephone'] ?? '',
          numeroSerie: deviceInfo?['numeroSerie'] ?? '',
        );
      }
    } on ServerException catch (e) {
      // Handle ServerException specifically
      debugPrint('CourseContentEntryForm: ServerException: $e');
    } catch (e) {
      // Handle other unexpected errors
      if (context.mounted) {
        CustomErrorDialog.show(
          context,
          message: "Une erreur inattendue est survenue: $e",
          title: "Erreur",
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CourseContentEntryCubit, CourseContentEntryState>(
      listener: (context, state) {
        if (state is CourseContentEntrySuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Saisie enregistrée avec succès",
            ).getSnackBar(),
          );
          widget.onSuccess(
            widget.scheduleItem.cours,
            widget.scheduleItem.professeur,
            widget.scheduleItem.heure,
          );
        } else if (state is CourseContentEntryError) {
          CustomErrorDialog.show(
          context,
          message: state.message,
          title: "Erreur de soumission",
        );
      }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Builder(
          builder: (context){
            return CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.cahierDeTexte,
                  title: "SAISIE CAHIER DE TEXTE",
                  isSearchBarVisible: false,
                  etablissementUtilisateur: widget.currentUser,
                  isActionButtonVisible: false
                ),
                SliverFillRemaining(
                  child:Padding(
                    padding: const EdgeInsets.symmetric( vertical: 8.0, horizontal: 20),
                    child: SingleChildScrollView(
                      child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        // Course info header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            spacing: 10,
                            children: [
                              Icon(
                                Icons.badge,
                                size: 24,
                                color: Colors.black,
                              ),
                              Text(
                                "${widget.scheduleItem.cours} / ${widget.scheduleItem.classe} / ${widget.scheduleItem.semestre}",
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16.0),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text.rich(
                                TextSpan(children: [
                                  const TextSpan(
                                    text: "Cours : ",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: widget.scheduleItem.cours,
                                    style: const TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ]
                              ),
                              ),
                              const SizedBox(height: 4),
                              Text.rich(
                                TextSpan(children: [
                                  const TextSpan(
                                    text: "Enseignant : ",
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  TextSpan(
                                    text: widget.scheduleItem.professeur,
                                    style: const TextStyle(
                                      fontSize: 10,
                                    ),
                                  ),
                                ]
                              ),
                              ),
                          const SizedBox(height: 8),
                        // Time fields
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Heure début *",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  // TextFormField for Heure début
                                  TextFormField(
                                    controller: _heureDebutController,
                                    readOnly: true, // Make it read-only to prevent manual input
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8.0),
                                        borderSide: BorderSide(color: Colors.grey[300]!),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8.0),
                                        borderSide: const BorderSide(color: Color(0xFF953324)),
                                      ),
                                    ),
                                    onTap: () => _pickTime(context, isStartTime: true),
                                    validator: (value) {
                                      if (_selectedHeureDebut == null) {
                                        return 'Veuillez sélectionner une heure de début';
                                      }
                                      // Validation: selected start time must not be earlier than parsed start time
                                      if (_parsedHeureDebut != null &&
                                          _isTimeBefore(_selectedHeureDebut!, _parsedHeureDebut!)) {
                                               ScaffoldMessenger.of(context).showSnackBar(
                                                  CustomSnackbar(
                                                    message: "Veuillez sélectionner les heures de début et de fin",
                                                    isError: true,
                                                  ).getSnackBar(),
                                                );
                                        return 'L\'heure de début ne peut pas être antérieure à l\'heure de début du cours';
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            const Icon(Icons.access_time_filled_outlined, size: 32),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    "Heure fin *",
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  // TextFormField for Heure fin
                                  TextFormField(
                                    controller: _heureFinController,
                                    readOnly: true, // Make it read-only to prevent manual input
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black,
                                    ),
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8.0),
                                        borderSide: BorderSide(color: Colors.grey[300]!),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8.0),
                                        borderSide: const BorderSide(color: Color(0xFF953324)),
                                      ),
                                    ),
                                    onTap: () => _pickTime(context, isStartTime: false),
                                    validator: (value) {
                                      if (_selectedHeureFin == null) {
                                        return 'Veuillez sélectionner une heure de fin';
                                      }
                                      // Validation: selected end time must not be earlier than parsed start time
                                      if (_parsedHeureDebut != null &&
                                          _isTimeBefore(_selectedHeureFin!, _parsedHeureDebut!)) {
                                        return 'L\'heure de fin ne peut pas être antérieure à l\'heure de début du cours';
                                      }
                                      // Validation: selected end time must not be earlier than selected start time
                                      if (_selectedHeureDebut != null &&
                                          _isTimeBefore(_selectedHeureFin!, _selectedHeureDebut!)) {
                                        return 'L\'heure de fin ne peut pas être antérieure à l\'heure de début sélectionnée';
                                      }
                                      return null;
                                    },
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        
                        // Observations field
                        const Text(
                          "Observations *",
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _observationsController,
                          maxLines: 6,
                          style: const TextStyle(
                            fontSize: 10
                          ),
                          decoration: InputDecoration(
                            hintText: "Saisissez vos observations",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: const BorderSide(color: Color(0xFF953324)),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // Submit button
                        BlocBuilder<CourseContentEntryCubit, CourseContentEntryState>(
                          builder: (context, state) {
                            final isLoading = state is CourseContentEntryLoading;
                            
                            return SizedBox(
                              width: double.infinity,
                              height: 48,
                              child: ElevatedButton(
                                onPressed: isLoading ? null : _submitForm,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF00BCD4),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                                child: isLoading
                                    ? const CircularProgressIndicator(color: Colors.white)
                                    : const Text(
                                        "CONFIRMER",
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                              ),
                            );
                          },
                        ),
                            ]
                          )
                          )
                          
                          ],
                                      ),
                    ),
                  ),
              ),
              ]
            );
          }
        ),
      ),
    );
  }
}
