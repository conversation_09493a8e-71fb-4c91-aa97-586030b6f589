// Models for handling different types of API error responses

/// Model for business logic error responses (e.g., user already exists)
class ApiErrorResponse {
  final int returnCode;
  final String message;
  final String userMessage;

  ApiErrorResponse({
    required this.returnCode,
    required this.message,
    required this.userMessage,
  });

  factory ApiErrorResponse.fromJson(Map<String, dynamic> json) {
    return ApiErrorResponse(
      returnCode: json['returnCode'],
      message: json['message'],
      userMessage: json['userMessage'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'returnCode': returnCode,
      'message': message,
      'userMessage': userMessage,
    };
  }
}

/// Model for validation error responses (e.g., missing required fields)
class ValidationErrorResponse {
  final String type;
  final Map<String, String>? parameters;
  final int status;
  final String? detail;

  ValidationErrorResponse({
    required this.type,
    this.parameters, // Optional parameter, defaults to empty map if not provided in JSON data
    required this.status,
    this.detail,
  });

  factory ValidationErrorResponse.fromJson(Map<String, dynamic> json) {
    return ValidationErrorResponse(
      type: json['type'],
      parameters: json['parameters'] ?? {},
      status: json['status'],
      detail: json['detail'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'parameters': parameters,
      'status': status,
      'detail': detail,
    };
  }
}
