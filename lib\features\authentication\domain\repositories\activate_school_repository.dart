import 'dart:async';

import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:kairos/features/schools/data/models/user_profile_model.dart';
import 'package:dartz/dartz.dart';

/// Abstract repository for school activation operations.
///
/// Defines the contract for activating a school using an activation request.
abstract class ActivateSchoolRepository {
  /// Activates a school using the provided [request].
  ///
  /// Returns a [Future] that completes with the result of the activation.
  /// The return type is dynamic as the specific success/failure response
  /// structure is not yet defined.
 
  /// Activate a school with the provided credentials.
  Future<Either<Failure, List<UserProfileModel>>> activateSchool(ActivationRequestEntity request);
}