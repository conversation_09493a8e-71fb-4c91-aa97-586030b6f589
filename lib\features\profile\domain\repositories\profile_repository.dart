import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/profile_entity.dart';
import '../entities/carte_virtuelle_entity.dart'; // New import for CarteVirtuelleEntity
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; // New import

/// Abstract repository for profile operations
abstract class ProfileRepository {
  /// Get user profile from local storage using codeUtilisateur
  Future<Either<Failure, ProfileEntity?>> getProfile(String codeUtilisateur);
  
  /// Get virtual card data for a specific user
  Future<Either<Failure, CarteVirtuelleEntity>> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Logout user by calling suppressionEtablissement API
  Future<Either<Failure, void>> logout(DeconnexionRequest request);
  
  /// Delete user account by calling suppressionCompte API
  Future<Either<Failure, void>> deleteAccount(DeleteAccountRequest request); // New method

  /// Remove user profile from local storage
  Future<Either<Failure, void>> removeProfile();
}
