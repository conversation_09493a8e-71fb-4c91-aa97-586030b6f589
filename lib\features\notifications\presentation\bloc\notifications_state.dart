﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/notification_entity.dart';

/// Base notifications state
abstract class NotificationsState extends Equatable {
  const NotificationsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial notifications state
class NotificationsInitial extends NotificationsState {
  const NotificationsInitial();
}

/// Loading state during notifications operations
class NotificationsLoading extends NotificationsState {
  const NotificationsLoading();
}

/// Notifications data loaded successfully
class NotificationsLoaded extends NotificationsState {
  final List<NotificationEntity> notifications;

  const NotificationsLoaded({required this.notifications});

  @override
  List<Object?> get props => [notifications];
}

/// Notifications error occurred
class NotificationsError extends NotificationsState {
  final String message;
  
  const NotificationsError(this.message);
  
  @override
  List<Object?> get props => [message];
}
