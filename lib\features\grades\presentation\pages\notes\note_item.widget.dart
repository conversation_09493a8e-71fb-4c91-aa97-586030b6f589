import 'package:kairos/features/grades/domain/entities/notes_evaluation_entity.dart';
import 'package:flutter/material.dart';

class NoteItem extends StatelessWidget {
  final NotesEvaluationEntity notesEvaluation;

  const NoteItem({
    super.key,
    required this.notesEvaluation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 7.0, vertical: 4.0),
      height: 55,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Theme.of(context).colorScheme.secondary, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Main content area
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 6.0, right: 6.0, top: 6.0, bottom: 6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Top row: Type and date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${notesEvaluation.typeDevoir.toUpperCase()} - ${_formatDate(notesEvaluation.dateDevoir)}',
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.21,
                        ),
                      ),
                      const Spacer(),
                      // Semester and class info
                      Wrap(
                        runAlignment: WrapAlignment.end,
                        children: [
                          Text(
                            '${notesEvaluation.classe} | ${notesEvaluation.semestre.toUpperCase()}',
                            style: const TextStyle(
                              fontSize: 7,
                              fontWeight: FontWeight.w400,
                              color: Colors.black,
                              height: 1.21,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  // Middle row: Course name
                  Text(
                    notesEvaluation.cours,
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                      height: 1.21,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  // Bottom row: Teacher info
                  Text(
                    'Avec ${notesEvaluation.professeur}',
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.w400,
                      color: Colors.black.withValues(alpha: 0.5),
                      height: 1.21,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          // Left colored indicator with grade
          Container(
            width: 55,
            decoration: BoxDecoration(
              color: notesEvaluation.note >= 10 ? const Color(0xFF055A28) : const Color(0xFF920000),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Grade value
                Text(
                  notesEvaluation.note.toString(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    height: 1.21,
                  ),
                ),
                // Class average
                Text(
                  'Moy: ${notesEvaluation.moyenneClasse}',
                  style: const TextStyle(
                    fontSize: 8,
                    fontWeight: FontWeight.w400,
                    color: Colors.white,
                    height: 1.21,
                  ),
                ),
              ],
            ),
          ),
          
        ],
      ),
    );
  }

  /// Format date from API format (YYYY-MM-DD) to display format (DD/MM/YYYY)
  String _formatDate(String apiDate) {
    try {
      final parts = apiDate.split('-');
      if (parts.length == 3) {
        return '${parts[2]}/${parts[1]}/${parts[0]}';
      }
      return apiDate;
    } catch (e) {
      return apiDate;
    }
  }
}
