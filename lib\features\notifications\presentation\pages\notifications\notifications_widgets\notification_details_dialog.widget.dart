import 'package:flutter/material.dart';
import 'package:kairos/features/notifications/domain/entities/notification_entity.dart';
import 'package:flutter_html/flutter_html.dart';

/// A dialog widget to display the detailed content of a notification.
///
/// This widget is a [StatelessWidget] and takes a [NotificationEntity]
/// as a required parameter in its constructor. It provides a clean
/// and focused UI for notification details, adapted from a similar
/// educational resource dialog.
class NotificationDetailsDialog extends StatelessWidget {
  /// Creates a [NotificationDetailsDialog].
  ///
  /// The [notification] parameter is required and contains all the
  /// details to be displayed in the dialog.
  const NotificationDetailsDialog({
    super.key,
    required this.notification,
  });

  /// The notification entity containing the details to display.
  final NotificationEntity notification;

  /// Generates initials from a given title string.
  ///
  /// If the title is empty, returns 'NN' (Notification Initials).
  /// If the title has two or more words, it takes the first letter
  /// of the first two words. Otherwise, it takes the first two letters
  /// of the title.
  String _getInitials(String title) {
    if (title.isEmpty) return 'NN';

    final words = title.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    } else {
      return title.length >= 2
          ? title.substring(0, 2).toUpperCase()
          : title.toUpperCase();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 10),
      child: SizedBox(
        width: 300,
        height: 527,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // Main dialog container
            Positioned(
              left: 0,
              top: 28,
              child: Container(
                width: 300,
                height: 540,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: Colors.black.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Header with avatar and title
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24.0),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 42,
                                height: 41,
                                decoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor, // Always use primary color
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    _getInitials(notification.title), // Use notification title for initials
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 7),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      notification.title, // Display notification title
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 16,
                                        color: Color(0xFF8E0101),
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'De: ${notification.sender} le ${notification.formattedDate}', // Display sender and formatted date
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 9,
                                        color: Colors.black.withValues( alpha: 0.5),
                                      ),
                                    ),
                                    // Removed the class text section
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 15),
                          // Object/Subject
                          Row(
                            children: [
                              RichText(
                                text: TextSpan(
                                  style: const TextStyle(
                                    fontSize: 11,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  children: <TextSpan>[
                                    const TextSpan(
                                      text: 'Objet : ',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w800,
                                      ),
                                    ),
                                    TextSpan(
                                      text: notification.type.name, // Display notification type as string
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 7),
                    Container(
                      width: 250,
                      height: 360,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.black.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(15),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Description/Content
                            Expanded(
                              child: SingleChildScrollView(
                                child: Html(data: notification.mainContent), // Use Html widget for main content
                              ),
                            ),
                            // Removed the attachment section
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Close button
            Positioned(
              right: -10,
              top: 0,
              child: GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 44,
                  height: 42,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.8),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1,
                    ),
                  ),
                  child: const Center(
                    child: Text(
                      'X',
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 24,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}