import '../../domain/entities/sms_response_entity.dart';

/// Represents the response data model after sending an SMS.
/// This model is used for serialization and deserialization.
class SmsResponseModel extends SmsResponseEntity {
  const SmsResponseModel({
    required super.id,
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required super.deleted,
    required super.activated,
    required super.version,
    required super.dateCreated,
    required super.lastUpdated,
  });

  /// Creates a [SmsResponseModel] from a JSON map.
  factory SmsResponseModel.fromJson(Map<String, dynamic> json) {
    return SmsResponseModel(
      id: json['id'] as String,
      numeroTelephone: json['numeroTelephone'] as String,
      marqueTelephone: json['marqueTelephone'] as String,
      modelTelephone: json['modelTelephone'] as String,
      imeiTelephone: json['imeiTelephone'] as String,
      numeroSerie: json['numeroSerie'] as String,
      deleted: json['deleted'] as bool,
      activated: json['activated'] as bool,
      version: json['version'] as int,
      dateCreated: DateTime.parse(json['dateCreated'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  // No need for a toEntity method as it extends the entity
}