import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/enfant_tuteur_entity.dart';
import '../../domain/repositories/student_records_repository.dart';
import '../datasources/student_records_remote_datasource.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Implementation of StudentRecordsRepository
class StudentRecordsRepositoryImpl implements StudentRecordsRepository {
  final StudentRecordsRemoteDataSource remoteDataSource;

  StudentRecordsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<EnfantTuteurEntity>>> getEnfantsDuTuteur({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  }) async {
    try {
      final children = await remoteDataSource.getEnfantsDuTuteur(
        codeUtilisateur: codeUtilisateur,
        codeEtab: codeEtab,
        telephone: telephone,
      );
      
      // Map EnfantTuteurModel to EnfantTuteurEntity entities
      final List<EnfantTuteurEntity> childrenEntities = children.map((child) => child.toEntity()).toList();
      return Right(childrenEntities);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to load children: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure('${AppConstants.unknownErrorMessage}: $e'));
    }
  }
}
