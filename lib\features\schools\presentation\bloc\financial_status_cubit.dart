import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/check_financial_status_usecase.dart';
import 'financial_status_state.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Financial Status Cubit for managing financial status check operations
class FinancialStatusCubit extends Cubit<FinancialStatusState> {
  /// Use case for checking financial status.
  final CheckFinancialStatusUseCase checkFinancialStatusUseCase;

  /// Constructor for FinancialStatusCubit.
  FinancialStatusCubit(this.checkFinancialStatusUseCase) : super(const FinancialStatusInitial());

  /// Check financial status for a student
  Future<void> checkFinancialStatus({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(FinancialStatusLoading(codeEtudiant: codeEtudiant)); // Indicate loading state

    final params = CheckFinancialStatusParams(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );

    final failureOrFinancialStatus = await checkFinancialStatusUseCase(params);
    debugPrint('FINANCIAL STATUS CUBIT --> FinancialStatusEntity: $failureOrFinancialStatus');

    failureOrFinancialStatus.fold(
      (failure) {
        // Handle failure and emit error state
        emit(FinancialStatusError(_mapFailureToMessage(failure)));
      },
      (financialStatus) {
        // Emit success state with financial status
        emit(FinancialStatusSuccess(financialStatus: financialStatus));
      },
    );
  }

  /// Map Failure to a user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is AuthenticationFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return failure.message;
    } else {
      return AppConstants.unknownErrorMessage;
    }
  }
}
