/// Defines the result of a password reset operation.
/// This sealed class represents a union type, allowing for either a
/// successful outcome or a specific validation error.
sealed class ResetPasswordResult {}

/// Represents a successful password reset.
/// Contains a message confirming the success of the operation.
class ResetPasswordSuccess extends ResetPasswordResult {
  final String message;

  ResetPasswordSuccess(this.message);
}

/// Represents a validation error during the password reset process.
/// Provides detailed information about the error for debugging, user display,
/// and additional context.
class ResetPasswordValidationError extends ResetPasswordResult {
  final String debugMessage;
  final String userMessage;
  final String moreInfo;

  ResetPasswordValidationError({
    required this.debugMessage,
    required this.userMessage,
    required this.moreInfo,
  });
}