import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/notes_evaluation_entity.dart';
import '../repositories/notes_repository.dart';

/// Parameters for GetFilteredNotesEvaluationsUseCase
class GetFilteredNotesEvaluationsParams {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;
  final String dateDebut;
  final String dateFin;

  const GetFilteredNotesEvaluationsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
    required this.dateDebut,
    required this.dateFin,
  });
}

/// Use case for getting filtered notes evaluations by date range
class GetFilteredNotesEvaluationsUseCase implements UseCase<List<NotesEvaluationEntity>, GetFilteredNotesEvaluationsParams> {
  final NotesRepository repository;

  const GetFilteredNotesEvaluationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<NotesEvaluationEntity>>> call(GetFilteredNotesEvaluationsParams params) async {
    return await repository.getFilteredNotesEvaluations(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
      dateDebut: params.dateDebut,
      dateFin: params.dateFin,
    );
  }
}
