import 'package:flutter/material.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/dashboard_entity.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../datasources/dashboard_remote_datasource.dart';

/// Implementation of DashboardRepository
class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource remoteDataSource;

  DashboardRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, DashboardEntity>> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final dashboardModel = await remoteDataSource.loadDashboardData(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      return Right(dashboardModel.toEntity());
    } on ServerException catch (e) {
      debugPrint('DashboardRepositoryImpl: ServerException: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      debugPrint('DashboardRepositoryImpl: NetworkException: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      debugPrint('DashboardRepositoryImpl: DioException: ${e.message}');
      // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Erreur de connexion lors du chargement du dashboard: ${e.message}'));
    } catch (e) {
      debugPrint('DashboardRepositoryImpl: UnexpectedException: ${e.toString()}');
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
