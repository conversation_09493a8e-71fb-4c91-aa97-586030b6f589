﻿import 'package:equatable/equatable.dart';
import '../../domain/entities/profile_entity.dart';
import '../../domain/entities/carte_virtuelle_entity.dart'; // Import CarteVirtuelleEntity

/// Base profile state
abstract class ProfileState extends Equatable {
  const ProfileState();
  
  @override
  List<Object?> get props => [];
}

/// Initial profile state
class ProfileInitial extends ProfileState {
  const ProfileInitial();
}

/// Loading state during profile operations
class ProfileLoading extends ProfileState {
  const ProfileLoading();
}

/// Profile data loaded successfully
class ProfileLoaded extends ProfileState {
  final ProfileEntity profile;

  const ProfileLoaded({required this.profile});

  @override
  List<Object?> get props => [profile];
}

/// Profile not found (no user_profile in SharedPreferences)
class ProfileNotFound extends ProfileState {
  const ProfileNotFound();
}

/// Profile error occurred
class ProfileError extends ProfileState {
  final String message;

  const ProfileError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Logout loading state
class LogoutLoading extends ProfileState {
  const LogoutLoading();
}

/// Logout success state
class LogoutSuccess extends ProfileState {
  const LogoutSuccess();
}

/// Account deletion loading state
class ProfileDeleting extends ProfileState {
  const ProfileDeleting();
}

/// Account deletion success state
class ProfileDeleted extends ProfileState {
  const ProfileDeleted();
}

/// Virtual card loading state
class CarteVirtuelleLoading extends ProfileState {
  const CarteVirtuelleLoading();
}

/// Virtual card data loaded successfully
class CarteVirtuelleLoaded extends ProfileState {
  final CarteVirtuelleEntity carteVirtuelle;

  const CarteVirtuelleLoaded({required this.carteVirtuelle});

  @override
  List<Object?> get props => [carteVirtuelle];
}

/// Virtual card error occurred
class CarteVirtuelleError extends ProfileState {
  final String message;

  const CarteVirtuelleError(this.message);

  @override
  List<Object?> get props => [message];
}
