import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'password_reset_widgets/reinitialiser_mot_de_passe.widget.dart';
import 'password_reset_widgets/mot_de_passe_reinitialise.widget.dart';
import 'password_reset_widgets/mot_de_passe_oublie.widget.dart';

class PasswordResetPage extends StatefulWidget {
  const PasswordResetPage({super.key});

  @override
  State<PasswordResetPage> createState() => _PasswordResetPageState();
}

class _PasswordResetPageState extends State<PasswordResetPage> {
  final PageController _pageController = PageController();
  late String _selectedCodeEtab = '';

  void _onSchoolSelected(String codeEtab) {
    setState(() {
      _selectedCodeEtab = codeEtab;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: false,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 40),
                Expanded(

                  child: PageView(
                    physics: const NeverScrollableScrollPhysics(),
                    controller: _pageController,
                    children: [
                      MotDePasseOublieWidget(
                        pageController: _pageController,
                        onSchoolSelected: _onSchoolSelected,
                      ),
                      ReinitialiserMotDePasseWidget(
                        pageController: _pageController,
                        codeEtab: _selectedCodeEtab,
                      ),
                      MotDePasseReinitialiseWidget(pageController: _pageController),
                    ],
                  ),
                ),
                // Bottom decoration similar to accueil page
               Flexible(flex: 0, child: Image.asset("assets/images/logo_footer.png", height: 19)),
               SizedBox(height: 10)
              ],
            )
        
    );
  }
}
