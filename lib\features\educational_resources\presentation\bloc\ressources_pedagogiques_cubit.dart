import 'dart:convert';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressources_pedagogiques_usecase.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressources_pedagogiques_filtres_usecase.dart';
import 'package:kairos/features/educational_resources/domain/usecases/get_ressource_pedagogique_attachment_usecase.dart';
import 'package:kairos/features/educational_resources/presentation/bloc/ressources_pedagogiques_state.dart';

class RessourcesPedagogiquesCubit extends Cubit<RessourcesPedagogiquesState> {
  final GetRessourcesPedagogiquesUseCase getRessourcesPedagogiquesUseCase;
  final GetRessourcesPedagogiquesFiltresUseCase getRessourcesPedagogiquesFiltresUseCase;
  final GetRessourcePedagogiqueAttachmentUseCase getRessourcePedagogiqueAttachmentUseCase;

  // Store the currently loaded resources to preserve them during attachment operations
  List<ResourcePedagogiqueEntity> _currentRessources = [];

  RessourcesPedagogiquesCubit({
    required this.getRessourcesPedagogiquesUseCase,
    required this.getRessourcesPedagogiquesFiltresUseCase,
    required this.getRessourcePedagogiqueAttachmentUseCase,
  }) : super(RessourcesPedagogiquesInitial());

  /// Fetches educational resources for a student.
  /// This method handles the state management for fetching resources,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchRessourcesPedagogiques({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(RessourcesPedagogiquesLoading());
    final failureOrRessources = await getRessourcesPedagogiquesUseCase(
      GetRessourcesPedagogiquesParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrRessources.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Network Error'));
        } else {
          emit(RessourcesPedagogiquesError('Failed to fetch educational resources: Unknown Error'));
        }
      },
      (ressources) {
        _currentRessources = ressources; // Update the internal list
        emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches educational resources filtered by date range.
  /// This method handles the state management for fetching resources based on date range,
  /// emitting loading, error, or loaded states as appropriate.
  Future<void> fetchRessourcesPedagogiquesByDateRange({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? dateDebut,
    String? dateFin,
  }) async {
    emit(RessourcesPedagogiquesLoading());
    final failureOrRessources = await getRessourcesPedagogiquesFiltresUseCase(
      GetRessourcesPedagogiquesFiltresParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        dateDebut: dateDebut,
        dateFin: dateFin,
      ),
    );

    failureOrRessources.fold(
      (failure) {
        if (failure is ServerFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Server Error'));
        } else if (failure is NetworkFailure) {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Network Error'));
        } else {
          emit(RessourcesPedagogiquesError('Failed to fetch filtered educational resources: Unknown Error'));
        }
      },
      (ressources) {
        _currentRessources = ressources; // Update the internal list
        emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.initial));
      },
    );
  }

  /// Fetches attachment for a specific educational resource.
  /// This method preserves the current resources list while managing attachment state.
  Future<void> fetchRessourcePedagogiqueAttachment({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  }) async {
    // Update state to show attachment loading while preserving current resources
    if (state is RessourcesPedagogiquesLoaded) {
      emit((state as RessourcesPedagogiquesLoaded).copyWith(
        attachmentStatus: AttachmentStatus.loading,
      ));
    }

    final failureOrAttachment = await getRessourcePedagogiqueAttachmentUseCase(
      GetRessourcePedagogiqueAttachmentParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        idObject: idObject,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrAttachment.fold(
      (failure) {
        if (state is RessourcesPedagogiquesLoaded) {
          emit((state as RessourcesPedagogiquesLoaded).copyWith(
            attachmentStatus: AttachmentStatus.error,
            attachmentErrorMessage: 'Failed to fetch attachment',
          ));
        }
      },
      (attachment) async { // Added async here
        if (state is RessourcesPedagogiquesLoaded) {
          emit((state as RessourcesPedagogiquesLoaded).copyWith(
            attachmentStatus: AttachmentStatus.loaded,
          ));
        }
        // Check if the API returned success
        if (attachment.returnCode != 'SUCCESS') {
          // Emit error state for attachment while preserving the main resources list
          emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: 'Failed to fetch attachment: ${attachment.returnCode}'));
          return;
        }

        // Decode base64 content
        final bytes = base64Decode(attachment.file);

        // Get app documents directory
        final directory = await getApplicationDocumentsDirectory();

        // Generate filename based on type and current timestamp
        final extension = _getFileExtension(attachment.type);
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        debugPrint('RessourcesPedagogiquesCubit: Fetching attachment with params:');
        debugPrint('  codeEtab: $codeEtab');
        debugPrint('  telephone: $telephone');
        debugPrint('  codeEtudiant: $codeEtudiant');
        debugPrint('  extension: $extension');
        debugPrint('  codeUtilisateur: $codeUtilisateur');
        final finalFileName = 'ressource_attachment_$timestamp$extension';

        // Create file path
        final filePath = '${directory.path}/$finalFileName';

        // Write file to storage
        final file = File(filePath);
        await file.writeAsBytes(bytes);

        // Emit success state for attachment while preserving the main resources list
        emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.loaded));

        // Open file with system default application
        final result = await OpenFile.open(filePath, type: attachment.type);
        if (result.type != ResultType.done) {
          // Emit error state for attachment while preserving the main resources list
          emit(RessourcesPedagogiquesLoaded(_currentRessources, attachmentStatus: AttachmentStatus.error, attachmentErrorMessage: 'Failed to open file: ${result.message}'));
        }
      },
    );
  }
  /// Determines the file extension based on the attachment type.
  /// This is a helper method to map common MIME types to file extensions.
  String _getFileExtension(String type) {
    switch (type) {
      case 'application/pdf':
        return '.pdf';
      case 'image/jpeg':
        return '.jpg';
      case 'image/png':
        return '.png';
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return '.docx';
      case 'application/vnd.ms-excel':
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        return '.xlsx';
      case 'application/vnd.ms-powerpoint':
      case 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        return '.pptx';
      case 'text/plain':
        return '.txt';
      default:
        debugPrint('Unknown attachment type: $type. Defaulting to .bin');
        return '.bin'; // Default to binary if type is unknown
    }
  }
}
