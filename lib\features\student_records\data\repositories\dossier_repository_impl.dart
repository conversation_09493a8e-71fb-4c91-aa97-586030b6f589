import 'package:dartz/dartz.dart';
import 'package:kairos/core/error/exceptions.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/student_records/data/datasources/dossier_remote_data_source.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';
import 'package:kairos/features/student_records/domain/repositories/dossier_repository.dart';

class DossierRepositoryImpl implements DossierRepository {
  final DossierRemoteDataSource remoteDataSource;

  DossierRepositoryImpl({
    required this.remoteDataSource
  });

  @override
  Future<Either<Failure, List<DossierEntity>>> getDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    
      try {
        final remoteDossiers = await remoteDataSource.getDossiers(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
        // Assuming DossierModel has a toEntity() method
        return Right(remoteDossiers.map((model) => model.toEntity()).toList());
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } 
   
  }

  @override
  Future<Either<Failure, List<DossierEntity>>> getDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  }) async {
    try {
      final remoteDossiers = await remoteDataSource.getDossiersByYear(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        annee: annee,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(remoteDossiers.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, DossierAttachmentEntity>> getDossierAttachment({
    required String codeEtab,
    required String numeroTel,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  }) async {
    try {
      final remoteAttachment = await remoteDataSource.getDossierAttachment(
        codeEtab: codeEtab,
        numeroTel: numeroTel,
        codeEtudiant: codeEtudiant,
        idObject: idObject,
        codeUtilisateur: codeUtilisateur,
      );
      return Right(remoteAttachment.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}