


import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/common/hero_widget.dart';
import 'package:flutter/material.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart'; // Import EnfantDuTuteur

// ignore: must_be_immutable
class CustomAppBar extends StatefulWidget{

  const CustomAppBar({
    super.key,
    this.pageSection = HeaderEnum.dashboard,
    this.title,
    this.onSearchTap,
    this.isSearchBarVisible = false,
    this.isActionButtonVisible = true,
    this.etablissementUtilisateur, // Add optional parameter
    this.enfantDuTuteur, // Add optional parameter
  });

  final HeaderEnum pageSection;
  final String? title;
  final VoidCallback? onSearchTap;
  final bool? isActionButtonVisible;
  final bool isSearchBarVisible; // State to control search bar visibility
  final EtablissementUtilisateur? etablissementUtilisateur; // Declare parameter
  final EnfantTuteurEntity? enfantDuTuteur; // Declare parameter

 @override
 State<CustomAppBar> createState() => _CustomAppBarState();
}


class _CustomAppBarState extends State<CustomAppBar>{



  @override
  Widget build(BuildContext context){
    return SliverAppBar(
      pinned: true,
      forceMaterialTransparency: true,
      // Pass the etablissementUtilisateur to the HeroWidget
      flexibleSpace: HeroWidget(
        pageSection: widget.pageSection,
        etablissementUser: widget.etablissementUtilisateur,
        enfantDuTuteur: widget.enfantDuTuteur, // Pass enfantDuTuteur as well
      ),
      expandedHeight: 189,
      centerTitle: false,
      title: Text(widget.title ?? "", textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),),
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      actions: [
        if(widget.isActionButtonVisible!) IconButton(icon: Icon(
          widget.isSearchBarVisible? Icons.close: Icons.search,
        color: Colors.white),
        onPressed: widget.onSearchTap,),
      ],
    );
  }
}