import 'package:kairos/features/finances/data/models/frais_etudiant_model.dart';

class FinancesResponseModel {
  final double? montantEncaisser;
  final double? montantAEncaisser;
  final String? anneeReference;
  final List<FraisEtudiantModel>? fraisPayes;

  FinancesResponseModel({
    this.montantEncaisser,
    this.montantAEncaisser,
    this.anneeReference,
    this.fraisPayes,
  });

  factory FinancesResponseModel.fromJson(Map<String, dynamic> json) {
    return FinancesResponseModel(
      montantEncaisser: json['montantEncaisser']?.toDouble(),
      montantAEncaisser: json['montantAEncaisser']?.toDouble(),
      anneeReference: json['anneeReference'],
      fraisPayes: (json['fraisPayes'] as List<dynamic>?)
          ?.map((item) => FraisEtudiantModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'montantEncaisser': montantEncaisser,
      'montantAEncaisser': montantAEncaisser,
      'anneeReference': anneeReference,
      'fraisPayes': fraisPayes?.map((item) => item.toJson()).toList(),
    };
  }

  
}