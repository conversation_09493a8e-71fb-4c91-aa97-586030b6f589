import 'package:kairos/core/device_info.dart';

class RefreshTokenRequest extends DeviceInfo {
  final String codeOtp;
  RefreshTokenRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required this.codeOtp,
  });

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) {
    return RefreshTokenRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      codeOtp: json['codeOtp'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'codeOtp': codeOtp,
    });
    return json;
  }
}
