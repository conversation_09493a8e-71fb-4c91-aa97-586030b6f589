


import 'package:flutter/material.dart';
import 'package:kairos/core/utils/navigation_utils.dart';

class NewSchoolDialogWidget extends StatefulWidget {
  const NewSchoolDialogWidget({super.key});

  @override
  State<NewSchoolDialogWidget> createState() => _NewSchoolDialogWidgetState();
}


class  _NewSchoolDialogWidgetState extends State<NewSchoolDialogWidget> {
  @override
  Widget build(BuildContext context) {
    return  Dialog(
              insetPadding: EdgeInsets.symmetric(horizontal: 10),
              backgroundColor: Colors.white,
              child: Padding(
                  padding: const EdgeInsets.all(12.0),
                child: Column(
                      mainAxisSize: MainAxisSize.min,
                      spacing: 20,
                      children: [
                        Text("ACTIVATION D'UN NOUVEL ÉTABLISSEMENT", textAlign: TextAlign.center,style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor),),
                        SizedBox(height: 20),
                        DropdownButtonFormField(
                            hint: Text("Choisissez votre établissement"),
                        decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            ),
                        items: [
                          DropdownMenuItem(
                          value: "ESMT",
                          child: Text("ESMT"), 
                          ),
                          DropdownMenuItem(
                          value: "IAM",
                          child: Text("IAM"), 
                          ),
                        ], 
                        onChanged: (value){
                          debugPrint('VALUE PICKED BY THE USER');
                        }),

                        TextFormField(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: "Identifiant",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            ),
                          keyboardType: TextInputType.emailAddress,
                          validator: (value){
                            if(value!.isEmpty){
                              return "Veuillez saisir le code d'activation";
                            } else {
                              return null;
                            }
                          },
                          ),

                          TextFormField(
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: "Mot de passe",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                            ),
                          obscureText: true,
                          validator: (value){
                            if(value!.isEmpty){
                              return "Veuillez saisir votre mot de passe";
                            } else {
                              return null;
                            }
                          },
                          ),
                        SizedBox(height: 20),
                        Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        // mainAxisSize: MainAxisSize.min,
                        // spacing: 20,
                          children: [
                          FilledButton(
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.secondary),
                              minimumSize: WidgetStateProperty.all(Size(130, 50)),
                              ),
                            onPressed: (){
                            NavigationUtils.pop(context);
                          }, child: Text("ANNULER"),),
                            FilledButton(
                              style: ButtonStyle(
                                  minimumSize: WidgetStateProperty.all(Size(130, 50)),
                                  backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                                //  fixedSize: WidgetStateProperty.all(Size(300, 50))
                                  ),
                              onPressed: () {
                                debugPrint('the user clicked on `Continue` button');
                              },
                              child: Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold), ),
                              ),
                          ],
                        ),
                      ],
                    ),));


}
    
    }