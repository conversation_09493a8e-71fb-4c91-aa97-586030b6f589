import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart'; // Assuming qr_flutter is available
import 'package:flutter_bloc/flutter_bloc.dart'; // Import for BlocBuilder
import 'package:kairos/features/profile/presentation/bloc/profile_cubit.dart'; // Import for ProfileCubit
import 'package:kairos/features/profile/presentation/bloc/profile_state.dart'; // Import for ProfileState
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/features/profile/domain/entities/carte_virtuelle_entity.dart'; // Import for CarteVirtuelleEntity
import 'package:kairos/core/di/injection_container.dart' as di; // Import for sl
import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart'; // Import for AuthLocalDataSource

class CarteVirtuellePage extends StatefulWidget {
  final EtablissementUtilisateur school;
  final EnfantTuteurEntity? etudiant; // Made optional

  const CarteVirtuellePage({
    super.key,
    required this.school,
    this.etudiant, // Now optional
  });

  @override
  State<CarteVirtuellePage> createState() => _CarteVirtuellePageState();
}

class _CarteVirtuellePageState extends State<CarteVirtuellePage> {
    final AuthLocalDataSource _authLocalDataSource = di.sl<AuthLocalDataSource>();

  @override
  void initState() {
    super.initState();
    // Dispatch the event to fetch virtual card data when the page initializes
    _loadVirtualCard();
    
  }


  Future<void> _loadVirtualCard() async {
    try{
      final String? telephone = await _authLocalDataSource.getPhoneNumber();
      if(telephone != null){

        context.read<ProfileCubit>().getCarteVirtuelle(
          codeEtab: widget.school.codeEtab,
          telephone: telephone,
          codeEtudiant: widget.etudiant?.codeEtudiant ?? widget.school.codeUtilisateur,
          codeUtilisateur: widget.etudiant != null? widget.school.codeUtilisateur: null,
        );
      }
    } catch(e){
      debugPrint("error loading the cartevirtuelle: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;

    // Consolidate multiple Scaffold and AppBar instances into a single instantiation.
    // The AppBar is defined once here, and the body content changes based on the Bloc state.
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Ma carte virtuelle'),
        centerTitle: false,
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      // Refactor the widget tree to place all varying content, including any Builder logic,
      // within the body property of this sole Scaffold.
      body: BlocBuilder<ProfileCubit, ProfileState>(
        builder: (context, state) {
          CarteVirtuelleEntity? carteVirtuelle;
          bool isLoading = false;
          String? errorMessage;

          if (state is CarteVirtuelleLoading) {
            isLoading = true;
          } else if (state is CarteVirtuelleLoaded) {
            carteVirtuelle = state.carteVirtuelle;
          } else if (state is CarteVirtuelleError) {
            errorMessage = state.message;
          }

          // Display loading indicator when data is being fetched.
          if (isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          // Display an error message if data loading fails.
          if (errorMessage != null) {
            return Center(
              child: Text('Erreur de chargement de la carte virtuelle: $errorMessage'),
            );
          }

          // Display a message if virtual card information is not available.
          if (carteVirtuelle == null) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: Text('Impossible de charger les informations de la carte virtuelle.'),
              ),
            );
          }

          // Display the virtual card content when data is successfully loaded.
          return SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: heroHeight + 150, // Extended height to accommodate floating card
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      // Hero background image for visual appeal.
                      Hero(
                        tag: "hero_profile",
                        transitionOnUserGestures: true,
                        child: Image.asset(
                          "assets/images/header_dashboard.png",
                          width: MediaQuery.of(context).size.width,
                          fit: BoxFit.cover,
                        ),
                      ),
                      // Floating profile card positioned over the hero section.
                      Positioned(
                        top: 83, // 83px from screen top for optimal placement.
                        left: (MediaQuery.of(context).size.width - 302) / 2, // Centered horizontally.
                        child: _buildFloatingProfileCard(carteVirtuelle),
                      ),
                    ],
                  ),
                ),
                // QR code section displayed below the floating profile card.
                Padding(
                  padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
                  child: _buildQrCodeSection(carteVirtuelle),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFloatingProfileCard(CarteVirtuelleEntity carteVirtuelle) {
    return Stack(
      children: [
        Container(
          width: 302,
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.25),
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              const SizedBox(height: 206),
              const SizedBox(height: 13),
              Text(
                '${carteVirtuelle.prenom} ${carteVirtuelle.nom}',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w900,
                  color: Colors.black,
                  height: 1.21,
                ),
              ),
              const SizedBox(height: 7),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 12, color: Colors.black),
                  children: [
                    TextSpan(
                      text: '',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    TextSpan(text: carteVirtuelle.niveauEnCours),
                  ],
                ),
              ),
              // const SizedBox(height: 2),
              RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 12, color: Colors.black),
                  children: [
                    TextSpan(
                      text: '',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    TextSpan(text: carteVirtuelle.classeEnCours),
                  ],
                ),
              ),
              const SizedBox(height: 7),
            ],
          ),
        ),
        Positioned(
          left: 50.0,
          top: 10.0,
          child: _buildProfileImage(carteVirtuelle),
        ),
      ],
    );
  }

  Widget _buildProfileImage(CarteVirtuelleEntity carteVirtuelle) {
    ImageProvider backgroundImage;
    final String? photoData = carteVirtuelle.photo;

    if (photoData != null && photoData.isNotEmpty) {
      try {
        backgroundImage = MemoryImage(
          base64Decode(photoData.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
        );
      } catch (_) {
        backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
      }
    } else {
      backgroundImage = const AssetImage('assets/images/default_profile_image.jpg');
    }

    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.25),
            offset: const Offset(0, 4),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 200,
        backgroundImage: backgroundImage,
      ),
    );
  }

  Widget _buildQrCodeSection(CarteVirtuelleEntity carteVirtuelle) {

    final String qrData = jsonEncode({
      'codeUtilisateur': carteVirtuelle.codeUtilisateur,
      'prenom': carteVirtuelle.prenom,
      'nom': carteVirtuelle.nom,
      'profil': carteVirtuelle.profil,
      'programmeEnCours': carteVirtuelle.programmeEnCours,
      'anneeScolaireEnCours': carteVirtuelle.anneeScolaireEnCours,
      'niveauEnCours': carteVirtuelle.niveauEnCours,
      'classeEnCours': carteVirtuelle.classeEnCours,
      'dateDerniereInscription': carteVirtuelle.dateDerniereInscription,
      'montantAPayer': carteVirtuelle.montantAPayer,
      'montantPaye': carteVirtuelle.montantPaye
    });

    return Center(
      child: Column(
        children: [
          const SizedBox(height: 100),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 4),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center, // Center the children within the Stack
              children: [
                QrImageView(
                  data: qrData,
                  version: QrVersions.auto,
                  size: 200.0,
                  gapless: true,
                ),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white, // White background for the container
                    borderRadius: BorderRadius.circular(5), // Optional: slight rounded corners
                  ),
                  child: Image.memory(
                    base64Decode(widget.school.logoEtablissement.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
                    fit: BoxFit.contain, // Ensure the image fits within the container
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}

