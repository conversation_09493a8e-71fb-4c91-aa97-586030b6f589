
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import '../bloc/splash_cubit.dart';
import '../bloc/splash_state.dart';

class Splashscreen extends StatefulWidget{
  const Splashscreen({super.key});

  @override
  State<Splashscreen> createState() => _SplashscreenState();
}

class _SplashscreenState extends State<Splashscreen> with TickerProviderStateMixin{

  late final AnimationController _animationController;
  bool _didAnimationLoad = false;


  @override
  void initState(){
    super.initState();
    _animationController = AnimationController(vsync: this);

    _animationController.addStatusListener((status){
      if(status == AnimationStatus.completed){
        setState((){
          _didAnimationLoad = true;
          debugPrint("animation completed: $_didAnimationLoad");
          // Start authentication check after animation completes
          context.read<SplashCubit>().checkAuthenticationState();
        });
      }
    });
  }


  @override
  void dispose(){
    super.dispose();
    _animationController.dispose();
  }


  void _navigateBasedOnAuthState(BuildContext buildContext, SplashState state) {
    // Add a small delay before navigating to ensure the context is ready
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        if (!mounted) {
          debugPrint("Splashscreen widget is not mounted, cannot navigate.");
          return;
        }
        if (state is SplashAuthenticated) {
          // User is authenticated - navigate to liste_etablissement
          debugPrint("Attempting to navigate to liste_etablissement");
          Navigator.of(buildContext).pushReplacementNamed("/liste_etablissement");
          debugPrint("Navigation to liste_etablissement attempted.");
        } else if (state is SplashUnauthenticated) {
          // User is not authenticated - navigate to accueil
          debugPrint("Attempting to navigate to accueil");
          Navigator.of(buildContext).pushReplacementNamed("/accueil");
          debugPrint("Navigation to accueil attempted.");
        }
        // Note: SplashError will also default to accueil via SplashUnauthenticated
      } catch (e) {
        debugPrint("Error during navigation: $e");
      }
    }
    );
  }


  @override
  Widget build(BuildContext context){
    return BlocListener<SplashCubit, SplashState>(
      listener: (context, state) {
        _navigateBasedOnAuthState(context, state);
      },
      child: Scaffold(
        body: Center(
          child: Lottie.asset("assets/animations/kairos_lottie.json",
          controller: _animationController,
          onLoaded: (composition){
            _animationController.duration = composition.duration;
            _animationController.forward();
          },
          width: MediaQuery.of(context).size.width * .80,
          fit: BoxFit.contain
          ),
        ),
      ),
    );
  }
}