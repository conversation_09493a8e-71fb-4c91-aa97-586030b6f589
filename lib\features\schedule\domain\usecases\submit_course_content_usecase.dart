import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/emplois_du_temps_repository.dart';

/// UseCase for submitting course content entry
class SubmitCourseContentUseCase implements UseCase<String, SubmitCourseContentParams> {
  final EmploisDuTempsRepository repository;

  SubmitCourseContentUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(SubmitCourseContentParams params) async {
    return await repository.submitCourseContent(
      codeEtudiant: params.codeEtudiant,
      codeEtab: params.codeEtab,
      heureDebut: params.heureDebut,
      heureFin: params.heureFin,
      dateCours: params.dateCours,
      emploiDuTempsId: params.emploiDuTempsId,
      responsableId: params.responsableId,
      observations: params.observations,
      numeroTelephone: params.numeroTelephone,
      marqueTelephone: params.marqueTelephone,
      modelTelephone: params.modelTelephone,
      imeiTelephone: params.imeiTelephone,
      numeroSerie: params.numeroSerie,
    );
  }
}

/// Parameters for submitting course content
class SubmitCourseContentParams extends Equatable {
  final String codeEtudiant;
  final String codeEtab;
  final String heureDebut;
  final String heureFin;
  final String dateCours;
  final int emploiDuTempsId;
  final int responsableId;
  final String observations;
  final String numeroTelephone;
  final String marqueTelephone;
  final String modelTelephone;
  final String imeiTelephone;
  final String numeroSerie;

  const SubmitCourseContentParams({
    required this.codeEtudiant,
    required this.codeEtab,
    required this.heureDebut,
    required this.heureFin,
    required this.dateCours,
    required this.emploiDuTempsId,
    required this.responsableId,
    required this.observations,
    required this.numeroTelephone,
    required this.marqueTelephone,
    required this.modelTelephone,
    required this.imeiTelephone,
    required this.numeroSerie,
  });

  @override
  List<Object?> get props => [
        codeEtudiant,
        codeEtab,
        heureDebut,
        heureFin,
        dateCours,
        emploiDuTempsId,
        responsableId,
        observations,
        numeroTelephone,
        marqueTelephone,
        modelTelephone,
        imeiTelephone,
        numeroSerie,
      ];
}
