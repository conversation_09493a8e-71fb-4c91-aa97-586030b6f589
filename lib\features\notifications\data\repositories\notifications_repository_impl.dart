import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/notification_entity.dart';
import '../../domain/repositories/notifications_repository.dart';
import '../datasources/notifications_remote_datasource.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Implementation of NotificationsRepository
class NotificationsRepositoryImpl implements NotificationsRepository {
  final NotificationsRemoteDataSource remoteDataSource;

  NotificationsRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, List<NotificationEntity>>> getNotifications({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Get notifications from remote data source
      final notificationsResponse = await remoteDataSource.getNotifications(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert to entities and return
      final notifications = notificationsResponse.toNotificationEntities();
      return Right(notifications);
    } on ServerException catch (e) {
      // Handle server exceptions
      return Left(ServerFailure(e.message));
    } catch (e) {
      // Handle any other exceptions
      return Left(ServerFailure('${AppConstants.unknownErrorMessage}: ${e.toString()}'));
    }
  }
}
