import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/api/api_client.dart';
import '../../../../core/api/api_endpoints.dart';
import '../../../../core/api/api_exception.dart';
import '../../../../core/error/exceptions.dart';
import '../models/student_record.dart';

/// Abstract interface for student records remote data source
abstract class StudentRecordsRemoteDataSource {
  /// Get children/students for a tutor/parent
  Future<List<EnfantTuteurModel>> getEnfantsDuTuteur({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  });
}

/// Implementation of StudentRecordsRemoteDataSource
class StudentRecordsRemoteDataSourceImpl implements StudentRecordsRemoteDataSource {
  final ApiClient apiClient;

  StudentRecordsRemoteDataSourceImpl({required this.apiClient});

  @override
  Future<List<EnfantTuteurModel>> getEnfantsDuTuteur({
    required String codeUtilisateur,
    required String codeEtab,
    required String telephone,
  }) async {
    try {
      // Make HTTP GET request to the getEnfantsDuTuteur endpoint with query parameters
      final response = await apiClient.getWithToken(
        ApiEndpoints.getEnfantsDuTuteur,
        queryParameters: {
          'codeUtilisateur': codeUtilisateur,
          'codeEtab': codeEtab,
          'telephone': telephone.replaceAll("+", ""),
        },
        options: Options(responseType: ResponseType.bytes)
      );

      final decodedResponse = latin1.decode(response.data);
      final jsonResponse = jsonDecode(decodedResponse);

      // Debug print the raw response data
      debugPrint('StudentRecordsRemoteDataSourceImpl: getEnfantsDuTuteur raw response data: $jsonResponse');

      // Assuming the response data is a list of child objects
      if (jsonResponse is List) {
        final List<dynamic> childrenJsonList = jsonResponse;

        // Parse the list of child objects
        final List<EnfantTuteurModel> children = childrenJsonList
            .map((json) => EnfantTuteurModel.fromJson(json))
            .toList();

        return children;
      } else {
        // Handle unexpected response format
        throw ServerException('Unexpected response format');
      }
    } on DioException catch (e) {
      debugPrint("StudentRecordsRemoeDatasource: error ---> ${e.response!.statusCode}");

      if (e.response != null) {
        // Handle API error response
        final decodedResponse = latin1.decode(e.response!.data);
      final jsonResponse = jsonDecode(decodedResponse);
      debugPrint("StudentRecordsRemoeDatasource: jsonresponse ---> ${jsonResponse}");
        final apiException = ApiException.fromJson(
          jsonResponse,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors du chargement des enfants: $e');
    }
  }
}
