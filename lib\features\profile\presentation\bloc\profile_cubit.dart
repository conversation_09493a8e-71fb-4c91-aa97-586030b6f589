import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../domain/usecases/delete_account_usecase.dart'; // New import
import '../../domain/usecases/get_carte_virtuelle_usecase.dart'; // New import for virtual card use case
import '../../../authentication/data/models/deconnexion_request.dart';
import '../../../authentication/data/models/delete_account_request.dart'; // New import
import 'profile_state.dart';

/// Profile Cubit for managing profile state
class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfileUseCase;
  final LogoutUseCase _logoutUseCase;
  final DeleteAccountUseCase _deleteAccountUseCase; // New use case
  final GetCarteVirtuelleUseCase _getCarteVirtuelleUseCase; // New use case for virtual card

  ProfileCubit({
    required GetProfileUseCase getProfileUseCase,
    required LogoutUseCase logoutUseCase,
    required DeleteAccountUseCase deleteAccountUseCase, // New dependency
    required GetCarteVirtuelleUseCase getCarteVirtuelleUseCase, // New dependency for virtual card
  }) : _getProfileUseCase = getProfileUseCase,
       _logoutUseCase = logoutUseCase,
       _deleteAccountUseCase = deleteAccountUseCase, // Initialize new use case
       _getCarteVirtuelleUseCase = getCarteVirtuelleUseCase, // Initialize new use case
       super(const ProfileInitial());
  
  /// Load profile data for a specific user using codeUtilisateur
  Future<void> loadProfileData(String codeUtilisateur) async { // Accept codeUtilisateur
    emit(const ProfileLoading());

    // Pass the codeUtilisateur to the use case
    final failureOrProfile = await _getProfileUseCase(codeUtilisateur);

    failureOrProfile.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (profile) {
        if (profile != null) {
          emit(ProfileLoaded(profile: profile));
        } else {
          emit(const ProfileNotFound());
        }
      },
    );
  }
  
  /// Refresh profile data for a specific user using codeUtilisateur
  Future<void> refresh(String codeUtilisateur) async { // Accept codeUtilisateur
    // Pass the codeUtilisateur to loadProfileData
    await loadProfileData(codeUtilisateur);
  }

  /// Logout user
  Future<void> logout(DeconnexionRequest request) async {
    emit(const LogoutLoading());

    final failureOrSuccess = await _logoutUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const LogoutSuccess()),
    );
  }

  /// Delete user account
  Future<void> deleteAccount(DeleteAccountRequest request) async {
    emit(const ProfileDeleting());

    final failureOrSuccess = await _deleteAccountUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const ProfileDeleted()),
    );
  }

  /// Get virtual card data
  Future<void> getCarteVirtuelle({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const CarteVirtuelleLoading());

    final failureOrCarteVirtuelle = await _getCarteVirtuelleUseCase(
      GetCarteVirtuelleParams(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      ),
    );

    failureOrCarteVirtuelle.fold(
      (failure) => emit(CarteVirtuelleError(_mapFailureToMessage(failure))),
      (carteVirtuelle) => emit(CarteVirtuelleLoaded(carteVirtuelle: carteVirtuelle)),
    );
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet.';
    } else if (failure is CacheFailure) {
      return 'Erreur de stockage local.';
    } else {
      return 'Une erreur inattendue s\'est produite.';
    }
  }
}
