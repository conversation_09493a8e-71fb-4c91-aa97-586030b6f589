import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/features/authentication/data/models/reset_password_result.dart';
import '../../../domain/usecases/send_password_reset_email_usecase.dart';
import '../../../domain/usecases/reset_password_usecase.dart';
import '../../../data/datasources/auth_local_datasource.dart';
import '../state/password_reset_state.dart';
import '../../../../../core/error/failures.dart';

/// Cubit for managing password reset state
class PasswordResetCubit extends Cubit<PasswordResetState> {
  final SendPasswordResetEmailUseCase sendPasswordResetEmailUseCase;
  final ResetPasswordUseCase resetPasswordUseCase;
  final AuthLocalDataSource localDataSource;

  PasswordResetCubit({
    required this.sendPasswordResetEmailUseCase,
    required this.resetPasswordUseCase,
    required this.localDataSource,
  }) : super(const PasswordResetInitial());

  /// Send password reset email
  ///
  /// [email] - The user's email address
  Future<void> sendPasswordResetEmail(String email, String codeEtab) async {
    emit(const PasswordResetLoading());

    final result = await sendPasswordResetEmailUseCase.call(email, codeEtab);

    result.fold(
      (failure) {
        // Handle failure
        if (failure is ServerFailure) {
          emit(PasswordResetError(
            message: failure.message,
            returnCode: failure.returnCode,
          ));
        } else {
          emit(PasswordResetError(message: failure.message));
        }
      },
      (response) async {
        // Handle success - save email locally and emit success state
        await localDataSource.savePasswordResetEmail(email);
        emit(PasswordResetEmailSent(
          email: email,
          message: response.message ?? '',
        ));
      },
    );
  }

  /// Reset password with PIN and new password
  ///
  /// [email] - The user's email address
  /// [pin] - The verification PIN
  /// [newPassword] - The new password
  /// [confirmPassword] - The password confirmation
  Future<void> resetPassword(
    String codeEtab,
    String email,
    String pin,
    String newPassword,
    String confirmPassword,
  ) async {
    emit(const PasswordResetLoading());

    final result = await resetPasswordUseCase.call(codeEtab, email, pin, newPassword, confirmPassword);

    result.fold(
      (failure) {
        // Handle failure
        if (failure is ServerFailure) {
          emit(PasswordResetError(
            message: failure.message,
            returnCode: failure.returnCode,
          ));
        } else if (failure is ValidationFailure) {
          emit(PasswordResetError(message: failure.message));
        } else {
          emit(PasswordResetError(message: failure.message));
        }
      },
      (response) {
        // Handle success
        emit(PasswordResetSuccess(message: (response as ResetPasswordSuccess).message));
      },
    );
  }

  /// Get stored password reset email
  String? getStoredEmail() {
    return localDataSource.getPasswordResetEmail();
  }

  /// Reset to initial state
  void reset() {
    emit(const PasswordResetInitial());
  }

  /// Navigate to next step (for PageView navigation)
  void nextStep() {
    // This method can be used to trigger navigation in the UI
    // The actual navigation logic will be handled in the widgets
  }
}
