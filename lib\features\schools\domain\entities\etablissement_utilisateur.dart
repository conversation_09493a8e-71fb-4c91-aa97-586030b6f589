import 'package:equatable/equatable.dart';

/// Entity for user-associated school information
class EtablissementUtilisateur extends Equatable {
  final String libelleEtab;
  final String codeEtab;
  final String logoEtablissement;
  final String profil;
  final String codeUtilisateur;
  final String? nom;
  final String? prenom;

  const EtablissementUtilisateur({
    required this.libelleEtab,
    required this.codeEtab,
    required this.logoEtablissement,
    required this.profil,
    required this.codeUtilisateur,
    this.nom,
    this.prenom,
  });

  @override
  List<Object?> get props => [
        libelleEtab,
        codeEtab,
        logoEtablissement,
        profil,
        codeUtilisateur,
        nom,
        prenom,
      ];


      factory EtablissementUtilisateur.fromJson(Map<String, dynamic> json) {
    return EtablissementUtilisateur(
      libelleEtab: json['libelleEtab'],
      codeEtab: json['codeEtab'],
      logoEtablissement: json['logoEtablissement'],
      profil: json['profil'],
      codeUtilisateur: json['codeUtilisateur'],
      nom: json['nom'],
      prenom: json['prenom'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'libelleEtab': libelleEtab,
      'codeEtab': codeEtab,
      'logoEtablissement': logoEtablissement,
      'profil': profil,
      'codeUtilisateur': codeUtilisateur,
      'nom': nom,
      'prenom': prenom,
    };
  }
}