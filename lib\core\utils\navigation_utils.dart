import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

/// Utility class for consistent page transitions throughout the app
class NavigationUtils {
  // Default transition settings
  static const Duration _defaultDuration = Duration(milliseconds: 300);
  static const Curve _defaultCurve = Curves.easeInOut;
  static const PageTransitionType _defaultTransitionType = PageTransitionType.rightToLeft;

  /// Push a new page with transition
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget child, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
    Object? arguments,
  }) {
    return Navigator.push<T>(
      context,
      PageTransition<T>(
        type: type,
        child: child,
        duration: duration,
        curve: curve,
        inheritTheme: inheritTheme,
        isIos: isIos,
        ctx: inheritTheme ? context : null,
      ),
    );
  }

  /// Push a new page with transition using builder pattern
  static Future<T?> pushBuilder<T extends Object?>(
    BuildContext context,
    Widget Function(BuildContext) childBuilder, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
  }) {
    return Navigator.push<T>(
      context,
      PageTransition<T>(
        type: type,
        childBuilder: childBuilder,
        duration: duration,
        curve: curve,
        inheritTheme: inheritTheme,
        isIos: isIos,
        ctx: inheritTheme ? context : null,
      ),
    );
  }

  /// Push replacement with transition
  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget child, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
    TO? result,
  }) {
    return Navigator.pushReplacement<T, TO>(
      context,
      PageTransition<T>(
        type: type,
        child: child,
        duration: duration,
        curve: curve,
        inheritTheme: inheritTheme,
        isIos: isIos,
        ctx: inheritTheme ? context : null,
      ),
      result: result,
    );
  }

  /// Push named route with transition
  static Future<T?> pushNamed<T extends Object?>(
    BuildContext context,
    String routeName, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
    Object? arguments,
  }) {
    return Navigator.pushNamed<T>(
      context,
      routeName,
      arguments: arguments,
    );
  }

  /// Push replacement named route with transition
  static Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    BuildContext context,
    String routeName, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
    Object? arguments,
    TO? result,
  }) {
    return Navigator.pushReplacementNamed<T, TO>(
      context,
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  /// Push and remove until with transition
  static Future<T?> pushAndRemoveUntil<T extends Object?>(
    BuildContext context,
    Widget child,
    RoutePredicate predicate, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
  }) {
    return Navigator.pushAndRemoveUntil<T>(
      context,
      PageTransition<T>(
        type: type,
        child: child,
        duration: duration,
        curve: curve,
        inheritTheme: inheritTheme,
        isIos: isIos,
      ),
      predicate,
    );
  }

  /// Push named and remove until with transition
  static Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
    BuildContext context,
    String newRouteName,
    RoutePredicate predicate, {
    PageTransitionType type = _defaultTransitionType,
    Duration duration = _defaultDuration,
    Curve curve = _defaultCurve,
    bool inheritTheme = true,
    bool isIos = false,
    Object? arguments,
  }) {
    return Navigator.pushNamedAndRemoveUntil<T>(
      context,
      newRouteName,
      predicate,
      arguments: arguments,
    );
  }

  /// Pop with result
  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    Navigator.pop<T>(context, result);
  }

  /// Pop until route
  static void popUntil(BuildContext context, RoutePredicate predicate) {
    Navigator.popUntil(context, predicate);
  }

  /// Common transition types for different use cases
  static const PageTransitionType slideFromRight = PageTransitionType.rightToLeft;
  static const PageTransitionType slideFromLeft = PageTransitionType.leftToRight;
  static const PageTransitionType slideFromBottom = PageTransitionType.bottomToTop;
  static const PageTransitionType slideFromTop = PageTransitionType.topToBottom;
  static const PageTransitionType fadeTransition = PageTransitionType.fade;
  static const PageTransitionType scaleTransition = PageTransitionType.scale;
  static const PageTransitionType rotateTransition = PageTransitionType.rotate;
  static const PageTransitionType sizeTransition = PageTransitionType.size;
  static const PageTransitionType rightToLeftWithFade = PageTransitionType.rightToLeftWithFade;
  static const PageTransitionType leftToRightWithFade = PageTransitionType.leftToRightWithFade;
}
