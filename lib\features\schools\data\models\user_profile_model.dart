import 'user_model.dart';
import 'school_model.dart';
import '../../domain/entities/etablissement_utilisateur.dart';

/// Data model for user profile information from the API response
/// Represents the root object returned by /etablissement/check-liste-etablissements
class UserProfileModel {
  final String id;
  final String codeUtilisateur;
  final String profil;
  final String photo;
  final bool? isAuthenticated; // Optional as specified
  final String? message; // Optional as specified
  final String? nom; // Optional as specified
  final String? prenom; // Optional as specified
  final UserModel utilisateur;
  final SchoolModel etablissement;

  UserProfileModel({
    required this.id,
    required this.codeUtilisateur,
    required this.profil,
    required this.photo,
    this.isAuthenticated,
    this.message,
    required this.utilisateur,
    required this.etablissement,
    this.nom,
    this.prenom,
  });

  factory UserProfileModel.fromJson(Map<String, dynamic> json) {
    return UserProfileModel(
      id: json['id'],
      codeUtilisateur: json['codeUtilisateur'],
      profil: json['profil'],
      photo: json['photo'] ?? '',
      isAuthenticated: json['isAuthenticated'],
      message: json['message'],
      nom: json['nom'],
      prenom: json['prenom'],
      utilisateur: UserModel.fromJson(json['utilisateur']),
      etablissement: SchoolModel.fromJson(json['etablissement']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'codeUtilisateur': codeUtilisateur,
      'profil': profil,
      'photo': photo,
      'isAuthenticated': isAuthenticated,
      'nom': nom,
      'prenom': prenom,
      'message': message,
      'utilisateur': utilisateur.toJson(),
      'etablissement': etablissement.toJson(),
    };
  }

  /// Converts UserProfileModel to EtablissementUtilisateur entity
  /// This maintains compatibility with the existing domain layer
  EtablissementUtilisateur toEntity() {
    return EtablissementUtilisateur(
      libelleEtab: etablissement.libelleEtab,
      nom: nom,
      prenom: prenom,
      codeEtab: etablissement.codeEtab,
      logoEtablissement: etablissement.logoEtablissement,
      profil: profil,
      codeUtilisateur: codeUtilisateur,
    );
  }
}
