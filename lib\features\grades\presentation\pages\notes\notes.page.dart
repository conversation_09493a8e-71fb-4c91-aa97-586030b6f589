import 'package:kairos/features/grades/domain/entities/notes_evaluation_entity.dart';
import 'package:kairos/features/grades/presentation/bloc/notes_cubit.dart';
import 'package:kairos/features/grades/presentation/bloc/notes_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kairos/core/enums/header_enums.dart';
import 'package:kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:kairos/features/grades/presentation/pages/notes/note_item.widget.dart';
import 'package:kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:kairos/core/widgets/common/empty_message.widget.dart';

import 'package:kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:kairos/features/student_records/domain/entities/enfant_tuteur_entity.dart';
import 'package:kairos/core/di/injection_container.dart';


class NotesPage extends StatefulWidget {
  final EtablissementUtilisateur? school;
  final EnfantTuteurEntity? etudiant;

  const NotesPage({
    super.key,
    this.school,
    this.etudiant,
  });

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> with SingleTickerProviderStateMixin {
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  List<NotesEvaluationEntity> _filteredNotes = [];
  List<NotesEvaluationEntity> _allNotes = [];

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Animation controller
  late AnimationController _searchAnimationController;

  // Data sources
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  // Navigation arguments
  EtablissementUtilisateur? _school;
  EnfantTuteurEntity? _etudiant;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Add listener to filter notes when search text changes
    _searchController.addListener(_filterNotes);

    // Load navigation arguments and initialize data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadArgumentsAndData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  /// Load navigation arguments and initialize data
  void _loadArgumentsAndData() {
    final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    
    if (args != null) {
      _school = args['school'] as EtablissementUtilisateur?;
      _etudiant = args['etudiant'] as EnfantTuteurEntity?;
    } else {
      // Use constructor parameters if no route arguments
      _school = widget.school;
      _etudiant = widget.etudiant;
    }

    // Load notes data
    _loadNotesData();
  }

  /// Load notes data using BLoC
  Future<void> _loadNotesData() async {
    if (_school == null) return;

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && mounted) {
        // Determine parameters based on user profile
        final codeEtudiant = _etudiant?.codeEtudiant ?? _school!.codeUtilisateur;
        final codeUtilisateur = _etudiant != null ? _school!.codeUtilisateur : null;

        context.read<NotesCubit>().loadNotesEvaluations(
          codeEtab: _school!.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        );
      }
    } catch (e) {
      debugPrint('Error loading notes data: $e');
    }
  }

  /// Load notes data with date filter (server-side filtering)
  Future<void> _loadNotesDataWithDateFilter() async {
    if (_startDateFilter == null || _endDateFilter == null) {
      // If no date filter, use normal loading
      _loadNotesData();
      return;
    }

    if (_school == null) return;

    try {
      final phoneNumber = await _authLocalDataSource.getPhoneNumber();
      if (phoneNumber != null && mounted) {
        // Convert dates from dd/MM/yyyy to yyyy-MM-dd format for API
        final startDate = _convertDateToApiFormat(_startDateFilter!);
        final endDate = _convertDateToApiFormat(_endDateFilter!);

        // Determine parameters based on user profile
        final codeEtudiant = _etudiant?.codeEtudiant ?? _school!.codeUtilisateur;
        final codeUtilisateur = _etudiant != null ? _school!.codeUtilisateur : null;

        context.read<NotesCubit>().loadFilteredNotesEvaluations(
          codeEtab: _school!.codeEtab,
          telephone: phoneNumber,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
          dateDebut: startDate,
          dateFin: endDate,
        );
      }
    } catch (e) {
      debugPrint('Error loading filtered notes data: $e');
    }
  }

  /// Convert date from dd/MM/yyyy format to yyyy-MM-dd format for API
  String _convertDateToApiFormat(String dateStr) {
    try {
      final parts = dateStr.split('/');
      if (parts.length == 3) {
        // Convert from dd/MM/yyyy to yyyy-MM-dd
        return '${parts[2]}-${parts[1].padLeft(2, '0')}-${parts[0].padLeft(2, '0')}';
      }
      return dateStr;
    } catch (e) {
      debugPrint('Error converting date format: $e');
      return dateStr;
    }
  }

/// Method to filter notes based on search query (client-side text filtering only)
  void _filterNotes() {
    final String query = _searchController.text.toLowerCase();
    debugPrint("Filtering notes with query: '$query'");
    setState(() {
      try {
        _filteredNotes = _allNotes.where((note) {
          // Log the note details for debugging
          debugPrint('Processing note: ${note.typeDevoir}, ${note.cours}, ${note.professeur}, ${note.dateDevoir}, ${note.note}, ${note.semestre}');

          // Text search filter only (date filtering is now handled server-side)
          bool matchesText = (note.typeDevoir.toLowerCase()).contains(query) ||
                             (note.cours.toLowerCase()).contains(query) ||
                             (note.professeur.toLowerCase()).contains(query) ||
                             (note.dateDevoir.toLowerCase()).contains(query) ||
                             (note.note.toString()).contains(query) ||
                             (note.semestre.toLowerCase()).contains(query);

          return matchesText;
        }).toList();
      } catch (e, stack) {
        debugPrint('Error during _filterNotes: $e');
        debugPrint('Stack trace: $stack');
        // Optionally, clear filtered notes or show an error message to the user
        _filteredNotes = [];
      }
    });
  }

  /// Handle date filter change
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Trigger server-side filtering with date range
    _loadNotesDataWithDateFilter();
  }

  /// Clear date filter
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Load notes data without date filter (back to normal loading)
    _loadNotesData();
  }

  /// Method to toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear();
        _startDateFilter = null;
        _endDateFilter = null;
        // Load notes data without filters (back to normal loading)
        _loadNotesData();
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            isSearchBarVisible: _isSearchBarVisible,
            pageSection: HeaderEnum.notes,
            title: "ÉVALUATIONS & NOTES",
            onSearchTap: _toggleSearchBarVisibility,
            etablissementUtilisateur: widget.school,
            enfantDuTuteur: widget.etudiant,
          ),
          // Search bar
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * (_startDateFilter != null && _endDateFilter != null ? 100.0 : 60.0),
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterNotes(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher notes et évaluations...",
                  startDate: _startDateFilter,
                  endDate: _endDateFilter,
                ),
                pinned: true,
              );
            }
          ),
          // Content based on BLoC state
          BlocBuilder<NotesCubit, NotesState>(
            builder: (context, state) {
              if (state is NotesLoading) {
                return SliverFillRemaining(
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              } else if (state is NotesError) {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: state.message),
                  ),
                );
              } else if (state is NotesLoaded) {
                _allNotes = state.notesEvaluations;

                // Apply client-side text filtering to server-side filtered data
                // Do not call _filterNotes() here as it contains setState() and would cause an error during build.
                // The _searchController listener already handles filtering on text changes.
                if (_searchController.text.isNotEmpty) {
                  final String query = _searchController.text.toLowerCase();
                  _filteredNotes = _allNotes.where((note) {
                    // Ensure null-safety for all properties
                    bool matchesText = (note.typeDevoir.toLowerCase()).contains(query) ||
                                       (note.cours.toLowerCase()).contains(query) ||
                                       (note.professeur.toLowerCase()).contains(query) ||
                                       (note.dateDevoir.toLowerCase()).contains(query) ||
                                       (note.note.toString()).contains(query) ||
                                       (note.semestre.toLowerCase()).contains(query);
                    return matchesText;
                  }).toList();
                } else {
                  _filteredNotes = _allNotes;
                }

                if (_filteredNotes.isNotEmpty) {
                  return SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final note = _filteredNotes[index];
                        return NoteItem(notesEvaluation: note);
                      },
                      childCount: _filteredNotes.length,
                    ),
                  );
                } else {
                  return SliverFillRemaining(
                    child: Center(
                      child: EmptyMessage(message: "Aucune note trouvée"),
                    ),
                  );
                }
              } else {
                return SliverFillRemaining(
                  child: Center(
                    child: EmptyMessage(message: "Aucune note trouvée"),
                  ),
                );
              }
            },
          ),
        ]
      )
    );
  }
}
