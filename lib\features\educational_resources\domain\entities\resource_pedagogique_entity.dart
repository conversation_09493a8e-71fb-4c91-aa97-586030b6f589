import 'package:equatable/equatable.dart';
import 'document_entity.dart';

/// Domain entity for an educational resource item
class ResourcePedagogiqueEntity extends Equatable {
  final int idRessource;
  final String description;
  final String professeur;
  final String classe;
  final String dateAjout;
  final List<DocumentEntity> documents;

  const ResourcePedagogiqueEntity({
    required this.idRessource,
    required this.description,
    required this.professeur,
    required this.classe,
    required this.dateAjout,
    required this.documents,
  });

  @override
  List<Object?> get props => [
        idRessource,
        description,
        professeur,
        classe,
        dateAjout,
        documents,
      ];
}
