import 'package:dartz/dartz.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_entity.dart';
import 'package:kairos/features/student_records/domain/entities/dossier_attachment_entity.dart';

abstract class DossierRepository {
  /// Retrieves a list of dossiers for a specific student.
  ///
  /// Returns a [Future] that resolves to an [Either] containing a [Failure]
  /// on the left side if an error occurs, or a [List] of [DossierEntity]
  /// on the right side if the operation is successful.
  Future<Either<Failure, List<DossierEntity>>> getDossiers({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Retrieves a list of dossiers for a specific student filtered by year.
  ///
  /// Returns a [Future] that resolves to an [Either] containing a [Failure]
  /// on the left side if an error occurs, or a [List] of [DossierEntity]
  /// on the right side if the operation is successful.
  Future<Either<Failure, List<DossierEntity>>> getDossiersByYear({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String annee,
    String? codeUtilisateur,
  });

  /// Retrieves the attachment file for a specific dossier.
  ///
  /// Returns a [Future] that resolves to an [Either] containing a [Failure]
  /// on the left side if an error occurs, or a [DossierAttachmentEntity]
  /// on the right side if the operation is successful.
  Future<Either<Failure, DossierAttachmentEntity>> getDossierAttachment({
    required String codeEtab,
    required String numeroTel,
    required String codeEtudiant,
    required int idObject,
    String? codeUtilisateur,
  });
}