// Define states
import 'package:kairos/features/authentication/presentation/bloc/state/auth_state.dart';
import 'package:kairos/features/schools/domain/entities/etablissement_utilisateur.dart';

abstract class ActivateSchoolState extends AuthState {
  const ActivateSchoolState();
}

class ActivateSchoolInitial extends ActivateSchoolState {
  const ActivateSchoolInitial();
}

class ActivateSchoolLoading extends ActivateSchoolState {
  const ActivateSchoolLoading();
}

class ActivateSchoolSuccess extends ActivateSchoolState {
  final List<EtablissementUtilisateur> userProfiles;

  const ActivateSchoolSuccess(this.userProfiles);

  @override
  List<Object?> get props => [userProfiles];
}

class ActivateSchoolError extends ActivateSchoolState {
  final String errorMessage;

  const ActivateSchoolError(this.errorMessage);

  @override
  List<Object?> get props => [errorMessage];
}