import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notes_evaluation_entity.dart';

/// Abstract repository interface for notes operations
abstract class NotesRepository {
  /// Get notes evaluations for a student
  ///
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  Future<Either<Failure, List<NotesEvaluationEntity>>> getNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered notes evaluations for a student by date range
  ///
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  /// - [dateDebut]: Start date in YYYY-MM-DD format
  /// - [dateFin]: End date in YYYY-MM-DD format
  Future<Either<Failure, List<NotesEvaluationEntity>>> getFilteredNotesEvaluations({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    required String dateDebut,
    required String dateFin,
  });
}
