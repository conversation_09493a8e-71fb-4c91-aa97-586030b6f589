import '../../domain/entities/notification_entity.dart';

/// Response model for notifications API
class NotificationsResponseModel {
  final MessagesModel messages;

  NotificationsResponseModel({
    required this.messages,
  });

  factory NotificationsResponseModel.fromJson(Map<String, dynamic> json) {
    return NotificationsResponseModel(
      messages: MessagesModel.fromJson(json['messages'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'messages': messages.toJson(),
    };
  }

  /// Convert to unified list of notification entities
  List<NotificationEntity> toNotificationEntities() {
    final List<NotificationEntity> notifications = [];

    // Add SMS notifications
    for (final sms in messages.sms) {
      notifications.add(sms.toNotificationEntity());
    }

    // Add email notifications
    for (final email in messages.email) {
      notifications.add(email.toNotificationEntity());
    }

    // Sort by date (newest first)
    notifications.sort((a, b) => b.dateCreation.compareTo(a.dateCreation));

    return notifications;
  }
}

/// Messages container model
class MessagesModel {
  final List<SmsNotificationModel> sms;
  final List<EmailNotificationModel> email;

  MessagesModel({
    required this.sms,
    required this.email,
  });

  factory MessagesModel.fromJson(Map<String, dynamic> json) {
    return MessagesModel(
      sms: (json['sms'] as List<dynamic>?)
              ?.map((item) => SmsNotificationModel.fromJson(item))
              .toList() ??
          [],
      email: (json['email'] as List<dynamic>?)
              ?.map((item) => EmailNotificationModel.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sms': sms.map((item) => item.toJson()).toList(),
      'email': email.map((item) => item.toJson()).toList(),
    };
  }
}

/// SMS notification model
class SmsNotificationModel {
  final String telephone;
  final String dateCreation;
  final bool isSent;
  final String content;
  final int idObject;

  SmsNotificationModel({
    required this.telephone,
    required this.dateCreation,
    required this.isSent,
    required this.content,
    required this.idObject,
  });

  factory SmsNotificationModel.fromJson(Map<String, dynamic> json) {
    return SmsNotificationModel(
      telephone: json['telephone'] ?? '',
      dateCreation: json['dateCreation'] ?? '',
      isSent: json['isSent'] ?? false,
      content: json['content'] ?? '',
      idObject: json['idObject'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'telephone': telephone,
      'dateCreation': dateCreation,
      'isSent': isSent,
      'content': content,
      'idObject': idObject,
    };
  }

  /// Convert to notification entity
  NotificationEntity toNotificationEntity() {
    return NotificationEntity(
      idObject: idObject,
      content: content,
      dateCreation: _parseDate(dateCreation),
      isSent: isSent,
      type: NotificationTypeEntity.sms,
      telephone: telephone,
    );
  }

  /// Parse date string to DateTime
  DateTime _parseDate(String dateString) {
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // Fallback to current date if parsing fails
      return DateTime.now();
    }
  }
}

/// Email notification model
class EmailNotificationModel {
  final String email;
  final String dateCreation;
  final bool isSent;
  final String body;
  final String subject;
  final int idObject;

  EmailNotificationModel({
    required this.email,
    required this.dateCreation,
    required this.isSent,
    required this.body,
    required this.subject,
    required this.idObject,
  });

  factory EmailNotificationModel.fromJson(Map<String, dynamic> json) {
    return EmailNotificationModel(
      email: json['email'] ?? '',
      dateCreation: json['dateCreation'] ?? '',
      isSent: json['isSent'] ?? false,
      body: json['body'] ?? '',
      subject: json['subject'] ?? '',
      idObject: json['idObject'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'dateCreation': dateCreation,
      'isSent': isSent,
      'body': body,
      'subject': subject,
      'idObject': idObject,
    };
  }

  /// Convert to notification entity
  NotificationEntity toNotificationEntity() {
    return NotificationEntity(
      idObject: idObject,
      content: body, // Use body as main content
      dateCreation: _parseDate(dateCreation),
      isSent: isSent,
      type: NotificationTypeEntity.email,
      email: email,
      subject: subject,
      body: body,
    );
  }

  /// Parse date string to DateTime
  DateTime _parseDate(String dateString) {
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // Fallback to current date if parsing fails
      return DateTime.now();
    }
  }
}
