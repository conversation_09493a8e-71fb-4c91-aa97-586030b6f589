import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/password_reset_entity.dart';
import '../repositories/auth_repository.dart';

/// Use case for sending password reset email
class SendPasswordResetEmailUseCase {
  final AuthRepository repository;

  SendPasswordResetEmailUseCase(this.repository);

  /// Execute the send password reset email use case
  ///
  /// [email] - The user's email address
  /// Returns [Either<Failure, PasswordResetResponseEntity>] - Success or failure result
  Future<Either<Failure, PasswordResetResponseEntity>> call(String email, String codeEtab) async {
    return await repository.sendPasswordResetEmail(email, codeEtab);
  }
}
