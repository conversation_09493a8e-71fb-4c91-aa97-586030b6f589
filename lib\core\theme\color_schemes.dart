import 'package:flutter/material.dart';

/// Application color schemes for light and dark themes
class AppColorSchemes {
  // Primary colors
  static const Color primaryBlue = Color(0xFF08B3DF);
  static const Color secondaryGray = Color(0xFF95A1AA);
  static const Color accentAmber = Colors.amber;
  
  // Status colors
  static const Color successGreen = Color(0xFF4CAF50);
  static const Color errorRed = Color(0xFF920000);
  static const Color cahierTexteHeaderColor = Color(0xFF953324);
  static const Color warningOrange = Color(0xFFFF9800);
  static const Color infoBlue = Color(0xFF2196F3);
  
  // Neutral colors
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color mediumGray = Color(0xFF9E9E9E);
  static const Color darkGray = Color(0xFF424242);
  
  /// Light color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primaryBlue,
    onPrimary: white,
    secondary: secondaryGray,
    onSecondary: white,
    tertiary: accentAmber,
    onTertiary: black,
    error: errorRed,
    onError: white,
    surface: white,
    onSurface: black,
    surfaceContainerHighest : lightGray,
    onSurfaceVariant: darkGray,
    outline: mediumGray,
    shadow: Color(0x1F000000),
    inverseSurface: darkGray,
    onInverseSurface: white,
    inversePrimary: Color(0xFF87CEEB),
  );
  
  /// Dark color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: primaryBlue,
    onPrimary: black,
    secondary: secondaryGray,
    onSecondary: black,
    tertiary: accentAmber,
    onTertiary: black,
    error: errorRed,
    onError: white,
    surface: Color(0xFF1E1E1E),
    onSurface: white,
    surfaceContainerHighest : Color(0xFF2C2C2C),
    onSurfaceVariant: Color(0xFFE0E0E0),
    outline: Color(0xFF737373),
    shadow: Color(0x3F000000),
    inverseSurface: lightGray,
    onInverseSurface: black,
    inversePrimary: Color(0xFF006B8A),
  );
}
