import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/absence_retard_entity.dart';
import '../../domain/repositories/absences_retards_repository.dart';
import '../datasources/absences_retards_remote_datasource.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Implementation of AbsencesRetardsRepository
class AbsencesRetardsRepositoryImpl implements AbsencesRetardsRepository {
  final AbsencesRetardsRemoteDataSource remoteDataSource;

  AbsencesRetardsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<AbsenceRetardEntity>>> getAbsencesRetards({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final absencesRetardsModels = await remoteDataSource.getAbsencesRetards(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert models to entities
      final absencesRetardsEntities = absencesRetardsModels.map((model) => model.toEntity()).toList();
      
      return Right(absencesRetardsEntities);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return Left(AuthenticationFailure('Authentication failed'));
      } else if (e.response?.statusCode == 404) {
        return Left(NotFoundFailure('Absences and tardiness records not found'));
      } else if (e.response?.statusCode == 500) {
        return Left(ServerFailure('Server error occurred'));
      } else {
        return Left(NetworkFailure('Network error: ${e.message}'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure('${AppConstants.unknownErrorMessage}: $e'));
    }
  }

  @override
  Future<Either<Failure, List<AbsenceRetardEntity>>> getAbsencesRetardsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  }) async {
    try {
      final absencesRetardsModels = await remoteDataSource.getAbsencesRetardsFiltres(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
        startDate: startDate,
        endDate: endDate,
      );

      // Convert models to entities
      final absencesRetardsEntities = absencesRetardsModels.map((model) => model.toEntity()).toList();
      
      return Right(absencesRetardsEntities);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return Left(AuthenticationFailure('Authentication failed'));
      } else if (e.response?.statusCode == 404) {
        debugPrint('Filtered absences and tardiness records not found: ---> ${e.response?.data}');
        return Left(NotFoundFailure('Filtered absences and tardiness records not found:'));
      } else if (e.response?.statusCode == 500) {
        return Left(ServerFailure('Server error occurred'));
      } else {
        return Left(NetworkFailure('Network error: ${e.message}'));
      }
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure('${AppConstants.unknownErrorMessage}: $e'));
    }
  }
}
