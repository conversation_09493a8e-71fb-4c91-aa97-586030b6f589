import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/usecases/submit_course_content_usecase.dart';
import 'course_content_entry_state.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Cubit for managing course content entry state
class CourseContentEntryCubit extends Cubit<CourseContentEntryState> {
  final SubmitCourseContentUseCase submitCourseContentUseCase;

  CourseContentEntryCubit({
    required this.submitCourseContentUseCase,
  }) : super(const CourseContentEntryInitial());

  /// Submit course content entry
  Future<void> submitCourseContent({
    required String codeEtudiant,
    required String codeEtab,
    required String heureDebut,
    required String heureFin,
    required String dateCours,
    required int emploiDuTempsId,
    required int responsableId,
    required String observations,
    required String numeroTelephone,
    required String marqueTelephone,
    required String modelTelephone,
    required String imeiTelephone,
    required String numeroSerie,
  }) async {
    emit(const CourseContentEntryLoading());

    try {
      debugPrint('CourseContentEntryCubit: Submitting course content for emploiDuTempsId: $emploiDuTempsId');

      final result = await submitCourseContentUseCase(SubmitCourseContentParams(
        codeEtudiant: codeEtudiant,
        codeEtab: codeEtab,
        heureDebut: heureDebut,
        heureFin: heureFin,
        dateCours: dateCours,
        emploiDuTempsId: emploiDuTempsId,
        responsableId: responsableId,
        observations: observations,
        numeroTelephone: numeroTelephone,
        marqueTelephone: marqueTelephone,
        modelTelephone: modelTelephone,
        imeiTelephone: imeiTelephone,
        numeroSerie: numeroSerie,
      ));

      result.fold(
        (failure) {
          debugPrint('CourseContentEntryCubit: Failed to submit course content: ${failure.message}');
          emit(CourseContentEntryError(message: failure.message));
        },
        (successMessage) {
          debugPrint('CourseContentEntryCubit: Successfully submitted course content');
          emit(CourseContentEntrySuccess(message: successMessage));
        },
      );
    } catch (e) {
      debugPrint('CourseContentEntryCubit: ${AppConstants.unknownErrorMessage}: $e');
      emit(CourseContentEntryError(message: '${AppConstants.unknownErrorMessage}: $e'));
    }
  }
}
