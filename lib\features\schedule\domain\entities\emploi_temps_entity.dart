import 'package:equatable/equatable.dart';

/// Domain entity for schedule/timetable items
class EmploiTempsEntity extends Equatable {
  final String date;
  final String couleur;
  final String heure;
  final String salle;
  final String cours;
  final String professeur;
  final String classe;
  final String semestre;
  final String type;
  final int? emploiDuTempsId;
  final int? responsableId;

  const EmploiTempsEntity({
    required this.date,
    required this.couleur,
    required this.heure,
    required this.salle,
    required this.cours,
    required this.professeur,
    required this.classe,
    required this.semestre,
    required this.type,
    this.emploiDuTempsId,
    this.responsableId,
  });

  @override
  List<Object?> get props => [
        date,
        couleur,
        heure,
        salle,
        cours,
        professeur,
        classe,
        semestre,
        type,
        emploiDuTempsId,
        responsableId,
      ];
}
