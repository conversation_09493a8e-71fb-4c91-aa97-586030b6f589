
import 'package:kairos/features/profile/domain/entities/profile_entity.dart'; // Import ProfileEntity
/// Entity for child/student information for tutors/parents

/// Entity for child/student information for tutors/parents
class EnfantTuteurEntity extends ProfileEntity {
  final String codeEtudiant;
  final String prenom;
  final String nom;
  @override
  final String photo; // base64 string
  final String classeInscrite;

  const EnfantTuteurEntity({
    required this.codeEtudiant,
    required this.prenom,
    required this.nom,
    required this.photo,
    required this.classeInscrite,
    // These are now derived or defaulted, no longer required in this constructor
    // but passed to super
  }) : super(
          id: codeEtudiant, // Using codeEtudiant as id
          codeUtilisateur: codeEtudiant, // Using codeEtudiant as codeUtilisateur
          profil: 'ETU', // Assuming 'ETU' for student profile
          photo: photo,
          fullName: '$prenom $nom', // Derived from prenom and nom
          phoneNumber: '', // Default empty string, as not available in EnfantTuteurEntity
          schoolName: '', // Default empty string
          schoolCode: '', // Default empty string
          schoolLogo: '', // Default empty string
        );

  @override
  List<Object?> get props => [
        codeEtudiant,
        prenom,
        nom,
        photo,
        classeInscrite,
        ...super.props, // Include props from the base ProfileEntity
      ];
}
