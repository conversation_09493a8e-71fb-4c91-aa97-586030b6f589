import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/carte_virtuelle_entity.dart';
import '../repositories/profile_repository.dart';

/// Use case for getting virtual card data
class GetCarteVirtuelleUseCase implements UseCase<CarteVirtuelleEntity, GetCarteVirtuelleParams> {
  final ProfileRepository repository;

  GetCarteVirtuelleUseCase(this.repository);

  @override
  Future<Either<Failure, CarteVirtuelleEntity>> call(GetCarteVirtuelleParams params) async {
    return await repository.getCarteVirtuelle(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

class GetCarteVirtuelleParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetCarteVirtuelleParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [
        codeEtab,
        telephone,
        codeEtudiant,
        codeUtilisateur,
      ];
}