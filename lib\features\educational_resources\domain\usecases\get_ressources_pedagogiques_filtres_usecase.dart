import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:kairos/core/error/failures.dart';
import 'package:kairos/core/usecases/usecase.dart';
import 'package:kairos/features/educational_resources/domain/entities/resource_pedagogique_entity.dart';
import 'package:kairos/features/educational_resources/domain/repositories/ressources_pedagogiques_repository.dart';

/// Use case for fetching educational resources filtered by date range
class GetRessourcesPedagogiquesFiltresUseCase implements UseCase<List<ResourcePedagogiqueEntity>, GetRessourcesPedagogiquesFiltresParams> {
  final RessourcesPedagogiquesRepository repository;

  GetRessourcesPedagogiquesFiltresUseCase(this.repository);

  @override
  Future<Either<Failure, List<ResourcePedagogiqueEntity>>> call(
      GetRessourcesPedagogiquesFiltresParams params) async {
    return await repository.getRessourcesPedagogiquesFiltres(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
      dateDebut: params.dateDebut,
      dateFin: params.dateFin,
    );
  }
}

/// Parameters for GetRessourcesPedagogiquesFiltresUseCase
class GetRessourcesPedagogiquesFiltresParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;
  final String? dateDebut;
  final String? dateFin;

  const GetRessourcesPedagogiquesFiltresParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
    this.dateDebut,
    this.dateFin,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, codeUtilisateur, dateDebut, dateFin];
}
