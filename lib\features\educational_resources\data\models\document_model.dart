import '../../domain/entities/document_entity.dart';

/// Data model for a document attached to an educational resource
class DocumentModel {
  final int idDocument;
  final String nomDocument;

  DocumentModel({
    required this.idDocument,
    required this.nomDocument,
  });

  /// Factory constructor to create DocumentModel from JSON
  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      idDocument: json['idDocument'] ?? 0,
      nomDocument: json['nomDocument'] ?? '',
    );
  }

  /// Convert DocumentModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'idDocument': idDocument,
      'nomDocument': nomDocument,
    };
  }

  /// Convert DocumentModel to DocumentEntity
  DocumentEntity toEntity() {
    return DocumentEntity(
      idDocument: idDocument,
      nomDocument: nomDocument,
    );
  }
}
