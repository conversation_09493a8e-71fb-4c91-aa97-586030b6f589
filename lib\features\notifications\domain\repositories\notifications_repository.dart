import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/notification_entity.dart';

/// Repository interface for notifications data
abstract class NotificationsRepository {
  /// Get notifications for a student
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number from SharedPreferences
  /// - [codeEtudiant]: Student code (from etudiant object or school.codeUtilisateur)
  /// - [codeUtilisateur]: Optional user code from school object
  /// 
  /// Returns:
  /// - [Right]: List of [NotificationEntity] on success
  /// - [Left]: [Failure] on error
  Future<Either<Failure, List<NotificationEntity>>> getNotifications({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });
}
