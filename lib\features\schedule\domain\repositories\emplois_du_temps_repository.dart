import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/emploi_temps_entity.dart';

/// Abstract repository interface for schedule/timetable operations
abstract class EmploisDuTempsRepository {
  /// Get schedule/timetable data for a student
  /// 
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTemps({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  });

  /// Get filtered schedule/timetable data for a student with date range
  ///
  /// Parameters:
  /// - [codeEtab]: School code
  /// - [telephone]: Phone number
  /// - [codeEtudiant]: Student code
  /// - [codeUtilisateur]: User code (optional, used for PAR profile)
  /// - [startDate]: Start date for filtering (optional)
  /// - [endDate]: End date for filtering (optional)
  Future<Either<Failure, List<List<EmploiTempsEntity>>>> getEmploisDuTempsFiltres({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
    String? startDate,
    String? endDate,
  });

  /// Submit course content entry
  ///
  /// Parameters:
  /// - [codeEtudiant]: Student code
  /// - [codeEtab]: School code
  /// - [heureDebut]: Start time
  /// - [heureFin]: End time
  /// - [dateCours]: Course date
  /// - [emploiDuTempsId]: Schedule ID
  /// - [responsableId]: Responsible person ID
  /// - [observations]: Course observations/content
  /// - [numeroTelephone]: Phone number
  /// - [marqueTelephone]: Phone brand
  /// - [modelTelephone]: Phone model
  /// - [imeiTelephone]: Phone IMEI
  /// - [numeroSerie]: Phone serial number
  Future<Either<Failure, String>> submitCourseContent({
    required String codeEtudiant,
    required String codeEtab,
    required String heureDebut,
    required String heureFin,
    required String dateCours,
    required int emploiDuTempsId,
    required int responsableId,
    required String observations,
    required String numeroTelephone,
    required String marqueTelephone,
    required String modelTelephone,
    required String imeiTelephone,
    required String numeroSerie,
  });
}
