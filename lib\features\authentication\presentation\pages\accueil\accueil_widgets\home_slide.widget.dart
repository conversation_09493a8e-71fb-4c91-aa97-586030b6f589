

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_html/flutter_html.dart';
class HomeSlideWidget extends StatefulWidget{
  const HomeSlideWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<HomeSlideWidget> createState() => _HomeSlideWidgetState();
}

class _HomeSlideWidgetState extends State<HomeSlideWidget>{
  String? _htmlData;

  @override
  void initState(){
    super.initState();
    loadHtml();
  }


  Future<void> loadHtml() async{
    final data = await rootBundle.loadString("assets/documents/privacy_policy.html");
    setState(() {
      _htmlData = data;
    });
  }

  @override
  Widget build(BuildContext context){
    return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text("BIENVENUE SUR ", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
            SvgPicture.asset("assets/images/logo_kairos.svg"),
            SizedBox(height: 20, 
                    width: 200, 
                    child: Divider(color: Theme.of(context).primaryColor, thickness: 4,),),
            Spacer(),
            SvgPicture.asset("assets/images/bienvenue.svg"),
            Spacer(),
            SizedBox(height: 20),
            Spacer(),
            FilledButton(
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                  fixedSize: WidgetStateProperty.all(Size(300, 50))),
              onPressed: () {
                debugPrint('the user didAcceptPolicy AND clicked on `Continue` button');
                widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
              },
              child: Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold), ),
              ),
              SizedBox(height: 10,),
              Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              // child: Text("En cliquant sur `Continuer`, vous acceptez notre politique de confidentialité", 
              child: Text.rich(textAlign: TextAlign.center, TextSpan(
                children: [
                  TextSpan(text: "En cliquant sur "),
                  TextSpan(text: "CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(text: ", vous acceptez notre"),
                  TextSpan(text: " politique de confidentialité", 
                          recognizer: TapGestureRecognizer()..onTap = () async {
                            // final Uri url = Uri.parse("https://www.sensoft.sn/kairos/");
                            // if(await canLaunchUrl(url)){
                            //   await launchUrl(url);
                            // } else {
                            //   throw "Could not launch $url";
                            //   }
                            showModalBottomSheet(context: context, builder: (context) => SizedBox(
                              height: 1200,
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: CustomScrollView(
                                  slivers: [
                                    SliverToBoxAdapter(
                                      child: Column(
                                    children: [
                                      // Text("Politique de confidentialité", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                                      SizedBox(height: 7),],
                                  ),),
                                  SliverToBoxAdapter(
                                    child: Column(
                                      children: [
                                        // ignore: unterminated_string_literal
                                        // Text(
                                        // "KAIROS est une"
                                        // "application mobile développée par Sensoft pour faciliter l'accès aux services de l'éducation. En cliquant sur `Continuer`, vous acceptez notre politique de confidentialité. lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
                                        // "kairos is an ancient Greek word meaning 'the right or critical moment'. In modern Greek, kairos also means 'weather' or 'time'. It is one of two words that the ancient Greeks had for 'time'; the other being chronos. Whereas the latter refers to chronological or sequential time, kairos signifies a good or proper time for action. In this sense, while chronos is quantitative, kairos has a qualitative, permanent nature. The plural, kairoi means 'the times'. kairos is a term, idea, and practice that has been applied in several fields including classical rhetoric, modern rhetoric, digital media, Christian theology, and science. Wikipedia", 
                                        // textAlign: TextAlign.justify, style: TextStyle(fontSize: 12))
                                        _htmlData != null ? Html(data: _htmlData): CircularProgressIndicator(),
                                      ],),)
                                  ]
                                ),
                              ),
                            ),);
                          },
                          style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, decoration: TextDecoration.underline)),
                ]
              )),
              // textAlign: TextAlign.center, style: TextStyle(fontSize: 12)),
            ),
            Spacer(),
          
          ],
        );
  }
}