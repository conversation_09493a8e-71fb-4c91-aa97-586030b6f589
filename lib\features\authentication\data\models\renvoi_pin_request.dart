import 'package:kairos/core/device_info.dart';

class RenvoiPinRequest extends DeviceInfo {  RenvoiPinRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
  });

  factory RenvoiPinRequest.fromJson(Map<String, dynamic> json) {
    return RenvoiPinRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
    );
  }
}
