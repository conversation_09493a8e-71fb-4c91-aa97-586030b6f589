import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:kairos/features/authentication/presentation/bloc/state/password_reset_state.dart';
import 'package:kairos/features/authentication/presentation/bloc/cubit/password_reset_cubit.dart';
import 'package:kairos/core/widgets/common/snackbar_widget.dart';
import 'package:kairos/features/schools/presentation/pages/widgets/school_selection_modal.widget.dart';
import 'package:kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:kairos/core/widgets/dialogs/custom_error_dialog.widget.dart';


import 'package:kairos/features/schools/domain/entities/etablissement_entity.dart';

class MotDePasseOublieWidget extends StatefulWidget {
  final PageController pageController;
  final Function(String) onSchoolSelected;
  const MotDePasseOublieWidget({super.key, required this.pageController, required this.onSchoolSelected});

  @override
  State<MotDePasseOublieWidget> createState() => _MotDePasseOublieWidgetState();
}

class _MotDePasseOublieWidgetState extends State<MotDePasseOublieWidget> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _schoolController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  EtablissementEntity? _selectedSchool;

  @override
  void dispose() {
    _emailController.dispose();
    _schoolController.dispose();
    super.dispose();
  }

  /// Method to show the school selection bottom modal
  void _showSchoolSelectionModal() async {
    context.read<SchoolsCubit>().getSchools();
    final EtablissementEntity? selectedSchool = await showModalBottomSheet<EtablissementEntity?>(
      context: context,
      builder: (BuildContext context) {
        return SchoolSelectionModal();
      },
    );

    if (selectedSchool != null) {
      setState(() {
        _selectedSchool = selectedSchool;
        _schoolController.text = selectedSchool.libelleEtab;
        widget.onSchoolSelected(selectedSchool.codeEtab);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<PasswordResetCubit, PasswordResetState>(
      listener: (context, state) {
        if (state is PasswordResetEmailSent) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Code de réinitialisation envoyé à: ${state.email}",
            ).getSnackBar(),
          );
          // Navigate to next page
          widget.pageController.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        } else if (state is PasswordResetError) {
          // Show error message
          CustomErrorDialog.show(
          context,
          message: state.message,
          title: 'Erreur de validation',
          iconPath: 'assets/icons/icone_acces_refuse.svg',
          iconColor: Colors.red,
          barrierDismissible: false,
        );
        }
      },
      builder: (context, state) {
        _isLoading = state is PasswordResetLoading;
        
        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text(
              "MOT DE PASSE OUBLIÉ",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SvgPicture.asset("assets/images/logo_kairos.svg"),
            SizedBox(
              height: 20,
              width: 200,
              child: Divider(
                color: Theme.of(context).primaryColor,
                thickness: 5,
              ),
            ),
            const Spacer(),
            Flexible(flex: 8, child: SvgPicture.asset("assets/images/phone_auth.svg")),
            const Spacer(flex: 2),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Text(
                "Saisissez votre adresse e-mail pour recevoir le code de réinitialisation",
                textAlign: TextAlign.center,
              ),
            ),
            // const SizedBox(height: 20),
            const Spacer(flex: 2),
            Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20.0),
                child: Column(
                  children: [
                    // School selection field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "ETABLISSEMENT",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.lightBlueAccent,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        InkWell(
                          onTap: _showSchoolSelectionModal,
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                            decoration: BoxDecoration(
                              border: Border.all(color: Theme.of(context).primaryColor),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    _selectedSchool?.libelleEtab ?? "Sélectionner un établissement",
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: _selectedSchool == null ? Colors.grey.shade600 : Colors.black,
                                    ),
                                  ),
                                ),
                                Icon(Icons.arrow_drop_down, color: Colors.black),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10), // Spacing between school and email fields
                    // Email field
                    TextFormField(
                      controller: _emailController,
                      decoration: InputDecoration(
                        hintText: "Votre adresse e-mail",
                        hintStyle: TextStyle(color: Theme.of(context).colorScheme.secondary),
                        border: OutlineInputBorder(borderSide: BorderSide(color: Theme.of(context).primaryColor)),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 14,
                        ),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Veuillez saisir votre adresse e-mail";
                        }
                        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                          return "Veuillez saisir une adresse e-mail valide";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                fixedSize: WidgetStateProperty.all(const Size(300, 50)),
              ),
              onPressed: _isLoading ? null : () {
                debugPrint('User clicked on Continue button');
                if (_selectedSchool == null) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(
                      message: "Veuillez sélectionner un établissement.",
                    ).getSnackBar(),
                  );
                  return;
                }
                if (_formKey.currentState!.validate()) {
                  // Trigger password reset email sending via BLoC
                  context.read<PasswordResetCubit>().sendPasswordResetEmail(
                    _emailController.text.trim(),
                    _selectedSchool!.codeEtab, // Pass codeEtab
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    CustomSnackbar(
                      message: "Veuillez saisir une adresse e-mail valide",
                    ).getSnackBar(),
                  );
                }
              },
              child: _isLoading
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text("SUIVANT"),
            ),
            // const SizedBox(height: 20),
            const Spacer(),
          ],
        );
      },
    );
  }
}
