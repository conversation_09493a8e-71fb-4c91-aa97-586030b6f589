import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/repositories/finances_repository.dart';
import '../datasources/finances_remote_datasource.dart';
import '../../domain/entities/finances_response_entity.dart'; // Import the new entity
import '../../domain/entities/finances_unpaid_response_entity.dart';

/// Implementation of FinancesRepository
class FinancesRepositoryImpl implements FinancesRepository {
  final FinancesRemoteDataSource remoteDataSource;

  FinancesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, FinancesResponseEntity>> getPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      // Call the data source method which now returns FinancesResponseModel
      final financesResponseModel = await remoteDataSource.getPaidFees(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert the model to the new domain entity
      final financesResponseEntity = FinancesResponseEntity.fromModel(financesResponseModel);

      return Right(financesResponseEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, FinancesResponseEntity>> getFilteredPaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    required String dateDebut,
    required String dateFin,
    String? codeUtilisateur,
  }) async {
    try {
      // Call the remote data source to get filtered paid fees
      final financesResponseModel = await remoteDataSource.getFilteredPaidFees(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        dateDebut: dateDebut,
        dateFin: dateFin,
        codeUtilisateur: codeUtilisateur,
      );

      // Convert the model to the domain entity
      final financesResponseEntity = FinancesResponseEntity.fromModel(financesResponseModel);

      return Right(financesResponseEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, FinancesUnpaidResponseEntity>> getUnpaidFees({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    try {
      final unpaidResponseModel = await remoteDataSource.getUnpaidFees(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
        codeUtilisateur: codeUtilisateur,
      );
      final unpaidResponseEntity = FinancesUnpaidResponseEntity.fromModel(unpaidResponseModel);
      debugPrint("FinancesRepositoryImpl: Unpaid fees response: $unpaidResponseEntity");
      return Right(unpaidResponseEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
