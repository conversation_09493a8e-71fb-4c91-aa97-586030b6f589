import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/notification_entity.dart';
import '../repositories/notifications_repository.dart';

/// Use case for getting notifications
class GetNotificationsUseCase implements UseCase<List<NotificationEntity>, GetNotificationsParams> {
  final NotificationsRepository repository;

  GetNotificationsUseCase(this.repository);

  @override
  Future<Either<Failure, List<NotificationEntity>>> call(GetNotificationsParams params) async {
    return await repository.getNotifications(
      codeEtab: params.codeEtab,
      telephone: params.telephone,
      codeEtudiant: params.codeEtudiant,
      codeUtilisateur: params.codeUtilisateur,
    );
  }
}

/// Parameters for GetNotificationsUseCase
class GetNotificationsParams extends Equatable {
  final String codeEtab;
  final String telephone;
  final String codeEtudiant;
  final String? codeUtilisateur;

  const GetNotificationsParams({
    required this.codeEtab,
    required this.telephone,
    required this.codeEtudiant,
    this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [codeEtab, telephone, codeEtudiant, codeUtilisateur];
}
