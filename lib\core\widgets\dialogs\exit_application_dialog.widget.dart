import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter/services.dart';
import 'package:kairos/core/theme/color_schemes.dart';

class ExitApplicationDialog extends StatelessWidget {
  const ExitApplicationDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: const Text(
        "QUITTER L'APPLICATION",
        textAlign: TextAlign.center,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/icons/icon_pop_up_information.svg',
            width: 50,
            height: 50,
            colorFilter: ColorFilter.mode(Theme.of(context).primaryColor, BlendMode.srcIn),
          ),
          const SizedBox(height: 16),
          const Text(
            "Voulez-vous vraiment quitter l'application?",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.black,
            ),
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actionsOverflowButtonSpacing: 10,
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColorSchemes.errorRed),
            padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 10, vertical: 10)),
            minimumSize: WidgetStateProperty.all(const Size(140, 50)),
          ),
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('ANNULER'),
        ),
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 10, vertical: 10)),
            minimumSize: WidgetStateProperty.all(const Size(140, 50)),
          ),
          onPressed: () {
            Navigator.of(context).pop(true);
            SystemNavigator.pop();
          },
          child: const Text('QUITTER'),
        ),
      ],
    );
  }
}
