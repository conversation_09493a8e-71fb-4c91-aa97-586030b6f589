﻿import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/get_notifications_usecase.dart';
import 'notifications_state.dart';
import 'package:kairos/core/constants/app_constants.dart';

/// Notifications Cubit for managing notifications state
class NotificationsCubit extends Cubit<NotificationsState> {
  final GetNotificationsUseCase getNotificationsUseCase;

  NotificationsCubit({
    required this.getNotificationsUseCase,
  }) : super(const NotificationsInitial());

  /// Load notifications data
  Future<void> loadNotificationsData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    emit(const NotificationsLoading());

    try {
      // Call the use case
      final result = await getNotificationsUseCase(
        GetNotificationsParams(
          codeEtab: codeEtab,
          telephone: telephone,
          codeEtudiant: codeEtudiant,
          codeUtilisateur: codeUtilisateur,
        ),
      );

      // Handle the result
      result.fold(
        (failure) => emit(NotificationsError(_mapFailureToMessage(failure))),
        (notifications) => emit(NotificationsLoaded(notifications: notifications)),
      );
    } catch (e) {
      emit(NotificationsError('${AppConstants.unknownErrorMessage}: ${e.toString()}'));
    }
  }

  /// Refresh notifications data
  Future<void> refresh({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
    String? codeUtilisateur,
  }) async {
    await loadNotificationsData(
      codeEtab: codeEtab,
      telephone: telephone,
      codeEtudiant: codeEtudiant,
      codeUtilisateur: codeUtilisateur,
    );
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return 'Erreur du serveur. Veuillez réessayer plus tard.';
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet.';
    } else {
      return 'Une erreur inattendue s\'est produite.';
    }
  }
}
